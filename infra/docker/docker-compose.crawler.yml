version: '3'

services:
  redis:
    image: redis:6.2
    networks:
      - crawler_network

  crawler:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ./backend:/app/backend
      - ./data:/app/data
    environment:
      - PYTHONPATH=/app/backend
      - DJANGO_SETTINGS_MODULE=config.django_settings
    command: >
      sh -c "cd /app/backend &&
             python manage.py migrate &&
             python manage.py crawl_ttkan https://www.ttkan.co/novel/chapters/sumingzhihuan-aiqianshuidewuzei"
    networks:
      - crawler_network

  worker:
    build:
      context: .
      dockerfile: Dockerfile
    command: python backend/novel/crawler/worker.py
    volumes:
      - ./backend:/app/backend
    environment:
      - PYTHONPATH=/app/backend
      - DJANGO_SETTINGS_MODULE=config.django_settings
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    networks:
      - crawler_network

  prometheus:
    image: prom/prometheus
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    ports:
      - "9090:9090"
    networks:
      - crawler_network
    depends_on:
      - crawler
      - worker

networks:
  crawler_network:
    driver: bridge

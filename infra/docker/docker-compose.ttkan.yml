version: '3'

services:
  redis:
    image: redis:6.2
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - crawler_network

  crawler:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=1
      - CRAWLER_CONCURRENT_REQUESTS=4
      - CRAWLER_DOWNLOAD_DELAY=2
    command: >
      python manage.py crawl_ttkan
      "https://www.ttkan.co/novel/chapters/sumingzhihuan-aiqianshuidewuzei"
      --mode master
    depends_on:
      - redis
    networks:
      - crawler_network
    volumes:
      - ./backend:/app/backend

  worker:
    build:
      context: .
      dockerfile: Dockerfile
    command: python backend/novel/crawler/worker.py
    volumes:
      - ./backend:/app/backend
    environment:
      - PYTHONPATH=/app/backend
      - DJANGO_SETTINGS_MODULE=config.django_settings
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - redis
    networks:
      - crawler_network

  prometheus:
    image: prom/prometheus
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
    ports:
      - "9090:9090"
    networks:
      - crawler_network
    depends_on:
      - crawler
      - worker

networks:
  crawler_network:
    driver: bridge

volumes:
  redis_data:

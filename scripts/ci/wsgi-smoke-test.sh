#!/bin/bash
# WSGI Smoke Test Script for CI Deployment Safety Net
# 防線一：CI部署入口煙霧測試

set -e

echo "🔥 Starting WSGI Smoke Test..."
echo "📂 Working directory: $(pwd)"

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 檢查 WSGI 應用配置
check_wsgi_application() {
    log_info "檢查 WSGI 應用配置..."
    
    # 檢查 wsgi.py 文件是否存在
    if [[ ! -f "backend/config/wsgi.py" ]]; then
        log_error "WSGI 配置文件不存在: backend/config/wsgi.py"
        return 1
    fi
    
    log_success "WSGI 配置文件存在"
    
    # 檢查 WSGI 應用是否可以導入
    log_info "測試 WSGI 應用導入..."
    
    cd backend
    
    # 設置 Python 路徑
    if [[ -z "$PYTHONPATH" ]]; then
        export PYTHONPATH="$(pwd)/backend"
    fi
    
    # 測試 WSGI 應用導入
    python3 -c "
import sys
import os
sys.path.insert(0, '$(pwd)/backend')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.django_settings')

try:
    from config.wsgi import application
    print('✅ WSGI application imported successfully')
    print(f'📋 Application type: {type(application)}')
    print(f'📋 Application module: {application.__module__}')
except ImportError as e:
    print(f'❌ Failed to import WSGI application: {e}')
    sys.exit(1)
except Exception as e:
    print(f'❌ Error loading WSGI application: {e}')
    sys.exit(1)
"
    
    if [[ $? -eq 0 ]]; then
        log_success "WSGI 應用導入測試通過"
    else
        log_error "WSGI 應用導入測試失敗"
        return 1
    fi
    
    cd ..
    return 0
}

# 檢查 Django 設置
check_django_settings() {
    log_info "檢查 Django 設置..."
    
    cd backend
    
    # 檢查 Django 設置是否正確
    python3 manage.py check --settings=config.django_settings --deploy
    
    if [[ $? -eq 0 ]]; then
        log_success "Django 設置檢查通過"
    else
        log_error "Django 設置檢查失敗"
        cd ..
        return 1
    fi
    
    cd ..
    return 0
}

# 檢查 Gunicorn 配置兼容性
check_gunicorn_compatibility() {
    log_info "檢查 Gunicorn 配置兼容性..."
    
    cd backend
    
    # 檢查 Gunicorn 是否可以加載 WSGI 應用
    log_info "測試 Gunicorn WSGI 應用加載..."
    
    # 使用 gunicorn --check-config 檢查配置
    python3 -c "
import sys
sys.path.insert(0, '$(pwd)/backend')

try:
    import gunicorn.app.wsgiapp
    from config.wsgi import application
    
    # 模擬 Gunicorn 加載過程
    print('✅ Gunicorn can import WSGI application')
    print(f'📋 WSGI callable: {callable(application)}')
    
    # 檢查 WSGI 應用是否可調用
    if not callable(application):
        print('❌ WSGI application is not callable')
        sys.exit(1)
    
    print('✅ WSGI application is callable')
    
except ImportError as e:
    print(f'❌ Failed to import Gunicorn or WSGI application: {e}')
    sys.exit(1)
except Exception as e:
    print(f'❌ Error checking Gunicorn compatibility: {e}')
    sys.exit(1)
"
    
    if [[ $? -eq 0 ]]; then
        log_success "Gunicorn 兼容性檢查通過"
    else
        log_error "Gunicorn 兼容性檢查失敗"
        cd ..
        return 1
    fi
    
    cd ..
    return 0
}

# 檢查環境變數配置
check_environment_config() {
    log_info "檢查環境變數配置..."
    
    # 檢查關鍵環境變數
    local required_vars=("DJANGO_SETTINGS_MODULE")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        else
            log_success "環境變數 $var 已設置: ${!var}"
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_warning "以下環境變數未設置: ${missing_vars[*]}"
        log_info "設置預設值..."
        export DJANGO_SETTINGS_MODULE="config.django_settings"
        log_success "已設置預設 DJANGO_SETTINGS_MODULE"
    fi
    
    return 0
}

# 主函數
main() {
    local exit_code=0
    
    log_info "🚀 開始 CI 部署入口煙霧測試..."
    echo ""
    
    # 檢查環境變數配置
    if ! check_environment_config; then
        log_error "環境變數配置檢查失敗"
        exit_code=1
    fi
    
    echo ""
    
    # 檢查 WSGI 應用配置
    if ! check_wsgi_application; then
        log_error "WSGI 應用配置檢查失敗"
        exit_code=1
    fi
    
    echo ""
    
    # 檢查 Django 設置
    if ! check_django_settings; then
        log_error "Django 設置檢查失敗"
        exit_code=1
    fi
    
    echo ""
    
    # 檢查 Gunicorn 兼容性
    if ! check_gunicorn_compatibility; then
        log_error "Gunicorn 兼容性檢查失敗"
        exit_code=1
    fi
    
    echo ""
    
    if [[ $exit_code -eq 0 ]]; then
        log_success "🎉 CI 部署入口煙霧測試全部通過！"
        log_info "✨ 部署安全網防線一：WSGI 應用加載驗證 - 正常"
    else
        log_error "💥 CI 部署入口煙霧測試失敗！"
        log_error "🚨 部署安全網防線一：WSGI 應用加載驗證 - 異常"
        log_info "📝 請檢查以上錯誤信息並修復問題"
    fi
    
    return $exit_code
}

# 執行主函數
main "$@"

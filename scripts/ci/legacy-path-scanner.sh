#!/bin/bash
# Legacy Path Scanner for CI Deployment Safety Net
# 防線二：遺留路徑靜態掃描

set -e

echo "🔍 Starting Legacy Path Scanner..."
echo "📂 Working directory: $(pwd)"

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 定義遺留路徑模式
declare -a LEGACY_PATTERNS=(
    "novel\.settings"
    "novel\.wsgi"
    "from novel\."
    "import novel\."
    "backend\.novel\."
    "novel\.urls"
    "novel\.models"
    "novel\.views"
    "novel\.admin"
    "novel\.apps"
    "novel\.migrations"
    "novel\.crawler"
    "DJANGO_SETTINGS_MODULE.*novel"
    "WSGI_APPLICATION.*novel"
)

# 定義要忽略的文件和目錄
IGNORE_DIRS=(
    ".git"
    "node_modules"
    "venv"
    "__pycache__"
    ".pytest_cache"
    "build"
    "dist"
    ".next"
    "coverage"
    "storybook-static"
    "sessionmanager-bundle"
    "logs"
    ".pnpm-store"
    ".lighthouseci"
)

# 定義要忽略的文件模式
IGNORE_FILES=(
    "*.log"
    "*.min.js"
    "*.map"
    "*.pyc"
    "*.pyo"
    "package-lock.json"
    "yarn.lock"
    "pnpm-lock.yaml"
    "*.md"
    "legacy-path-scanner.sh"
    "*/legacy-path-scanner.sh"
)

# 構建 grep 忽略參數
build_grep_excludes() {
    GREP_EXCLUDES=""
    for dir in "${IGNORE_DIRS[@]}"; do
        GREP_EXCLUDES="$GREP_EXCLUDES --exclude-dir=$dir"
    done

    for file in "${IGNORE_FILES[@]}"; do
        GREP_EXCLUDES="$GREP_EXCLUDES --exclude=$file"
    done
}

# 掃描遺留路徑引用
scan_legacy_paths() {
    local found_issues=0
    local temp_file="/tmp/legacy_scan_results.tmp"

    log_info "掃描遺留路徑引用..."

    for pattern in "${LEGACY_PATTERNS[@]}"; do
        log_info "檢查模式: $pattern"

        # 使用 grep 搜索遺留路徑模式
        if grep -r -n -E "$pattern" . $GREP_EXCLUDES 2>/dev/null > "$temp_file"; then
            if [[ -s "$temp_file" ]]; then
                log_error "發現遺留路徑引用 - 模式: $pattern"
                echo "----------------------------------------"
                cat "$temp_file"
                echo "----------------------------------------"
                found_issues=1
                echo ""
            fi
        fi
    done

    # 清理臨時檔案
    rm -f "$temp_file"

    return $found_issues
}

# 檢查 Django 設置文件中的遺留配置
check_django_settings() {
    log_info "檢查 Django 設置文件中的遺留配置..."

    local settings_files=(
        "backend/config/django_settings.py"
        "backend/config/settings.py"
        "backend/settings.py"
    )

    local found_issues=0

    for settings_file in "${settings_files[@]}"; do
        if [[ -f "$settings_file" ]]; then
            log_info "檢查設置文件: $settings_file"

            # 檢查 INSTALLED_APPS 中的遺留應用
            if grep -n "novel" "$settings_file" | grep -v "#" | grep -v "NovelWebsite"; then
                log_error "在 $settings_file 中發現遺留的 novel 應用引用"
                found_issues=1
            fi

            # 檢查 ROOT_URLCONF 中的遺留配置
            if grep -n "ROOT_URLCONF.*novel" "$settings_file"; then
                log_error "在 $settings_file 中發現遺留的 ROOT_URLCONF 配置"
                found_issues=1
            fi

            # 檢查 WSGI_APPLICATION 中的遺留配置
            if grep -n "WSGI_APPLICATION.*novel" "$settings_file"; then
                log_error "在 $settings_file 中發現遺留的 WSGI_APPLICATION 配置"
                found_issues=1
            fi
        fi
    done

    if [[ $found_issues -eq 0 ]]; then
        log_success "Django 設置文件檢查通過"
    fi

    return $found_issues
}

# 檢查 URL 配置中的遺留路徑
check_url_configs() {
    log_info "檢查 URL 配置中的遺留路徑..."

    local url_files=(
        "backend/config/urls.py"
        "backend/urls.py"
    )

    local found_issues=0

    for url_file in "${url_files[@]}"; do
        if [[ -f "$url_file" ]]; then
            log_info "檢查 URL 文件: $url_file"

            # 檢查是否有遺留的 novel 應用 URL 引用
            if grep -n "novel\." "$url_file" | grep -v "#"; then
                log_error "在 $url_file 中發現遺留的 novel 應用 URL 引用"
                found_issues=1
            fi
        fi
    done

    if [[ $found_issues -eq 0 ]]; then
        log_success "URL 配置檢查通過"
    fi

    return $found_issues
}

# 檢查 Docker 配置中的遺留路徑
check_docker_configs() {
    log_info "檢查 Docker 配置中的遺留路徑..."

    local docker_files=(
        "Dockerfile"
        "docker-compose.yml"
        "infra/docker/Dockerfile*"
        "infra/docker/docker-compose*.yml"
    )

    local found_issues=0

    for pattern in "${docker_files[@]}"; do
        for file in $pattern; do
            if [[ -f "$file" ]]; then
                log_info "檢查 Docker 文件: $file"

                # 檢查是否有遺留的 novel 路徑引用
                if grep -n "novel\." "$file" | grep -v "#" | grep -v "NovelWebsite"; then
                    log_error "在 $file 中發現遺留的 novel 路徑引用"
                    found_issues=1
                fi
            fi
        done
    done

    if [[ $found_issues -eq 0 ]]; then
        log_success "Docker 配置檢查通過"
    fi

    return $found_issues
}

# 檢查腳本和配置文件中的遺留路徑
check_scripts_and_configs() {
    log_info "檢查腳本和配置文件中的遺留路徑..."

    local config_patterns=(
        "*.sh"
        "*.py"
        "*.yml"
        "*.yaml"
        "*.json"
        "Makefile"
        ".env*"
    )

    local found_issues=0

    for pattern in "${config_patterns[@]}"; do
        while IFS= read -r -d '' file; do
            # 跳過忽略的文件
            local skip_file=false
            for ignore_pattern in "${IGNORE_FILES[@]}"; do
                if [[ "$file" == $ignore_pattern ]] || [[ "$(basename "$file")" == $ignore_pattern ]]; then
                    skip_file=true
                    break
                fi
            done

            if [[ "$skip_file" == true ]]; then
                continue
            fi

            # 檢查文件內容
            if grep -l "novel\." "$file" 2>/dev/null | grep -v "#" >/dev/null; then
                log_warning "在 $file 中可能存在遺留的 novel 路徑引用"
                # 顯示具體行號和內容
                grep -n "novel\." "$file" | grep -v "#" || true
                found_issues=1
            fi
        done < <(find . -name "$pattern" -type f -print0 2>/dev/null)
    done

    return $found_issues
}

# 主函數
main() {
    local exit_code=0

    log_info "🚀 開始遺留路徑靜態掃描..."
    echo ""

    # 構建 grep 忽略參數
    build_grep_excludes

    # 掃描遺留路徑引用
    if ! scan_legacy_paths; then
        log_error "遺留路徑掃描發現問題"
        exit_code=1
    else
        log_success "遺留路徑掃描通過"
    fi

    echo ""

    # 檢查 Django 設置文件
    if ! check_django_settings; then
        log_error "Django 設置文件檢查失敗"
        exit_code=1
    fi

    echo ""

    # 檢查 URL 配置
    if ! check_url_configs; then
        log_error "URL 配置檢查失敗"
        exit_code=1
    fi

    echo ""

    # 檢查 Docker 配置
    if ! check_docker_configs; then
        log_error "Docker 配置檢查失敗"
        exit_code=1
    fi

    echo ""

    if [[ $exit_code -eq 0 ]]; then
        log_success "🎉 遺留路徑靜態掃描全部通過！"
        log_info "✨ 部署安全網防線二：遺留路徑檢測 - 正常"
    else
        log_error "💥 遺留路徑靜態掃描發現問題！"
        log_error "🚨 部署安全網防線二：遺留路徑檢測 - 異常"
        log_info "📝 請修復以上發現的遺留路徑引用"
    fi

    return $exit_code
}

# 執行主函數
main "$@"

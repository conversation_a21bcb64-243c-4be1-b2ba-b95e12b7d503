#!/bin/bash
# Deployment Simulation Test for CI Safety Net
# 防線三：部署模擬測試

set -e

echo "🚀 Starting Deployment Simulation Test..."
echo "📂 Working directory: $(pwd)"

# 顏色定義
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 配置變數
COMPOSE_FILE="infra/docker/docker-compose.ci.yml"
COMPOSE_PROJECT="novel-ci-test"
TIMEOUT=300  # 5 分鐘超時
HEALTH_CHECK_RETRIES=30
HEALTH_CHECK_INTERVAL=10

# 清理函數
cleanup() {
    log_info "🧹 清理測試環境..."
    docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" down -v --remove-orphans 2>/dev/null || true
    docker network prune -f 2>/dev/null || true
    log_success "清理完成"
}

# 設置清理陷阱
trap cleanup EXIT

# 檢查 Docker 和 Docker Compose
check_prerequisites() {
    log_info "檢查前置條件..."
    
    # 檢查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安裝或不在 PATH 中"
        return 1
    fi
    
    # 檢查 Docker 服務
    if ! docker info &> /dev/null; then
        log_error "Docker 服務未運行"
        return 1
    fi
    
    # 檢查 Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安裝或不在 PATH 中"
        return 1
    fi
    
    # 檢查配置文件
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        log_error "Docker Compose 配置文件不存在: $COMPOSE_FILE"
        return 1
    fi
    
    log_success "前置條件檢查通過"
    return 0
}

# 啟動服務
start_services() {
    log_info "🚀 啟動 CI 部署模擬環境..."
    
    # 清理可能存在的舊容器
    cleanup
    
    # 構建並啟動服務
    log_info "構建並啟動服務..."
    if ! docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" up -d --build; then
        log_error "服務啟動失敗"
        return 1
    fi
    
    log_success "服務啟動命令執行完成"
    return 0
}

# 等待服務健康
wait_for_services() {
    log_info "⏳ 等待服務健康檢查..."
    
    local services=("postgres-ci" "redis-ci" "backend-ci")
    local retry_count=0
    
    while [[ $retry_count -lt $HEALTH_CHECK_RETRIES ]]; do
        local all_healthy=true
        
        for service in "${services[@]}"; do
            local health_status
            health_status=$(docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" ps -q "$service" | xargs docker inspect --format='{{.State.Health.Status}}' 2>/dev/null || echo "unknown")
            
            if [[ "$health_status" != "healthy" ]]; then
                log_info "服務 $service 狀態: $health_status"
                all_healthy=false
                break
            fi
        done
        
        if [[ "$all_healthy" == true ]]; then
            log_success "所有核心服務健康檢查通過"
            return 0
        fi
        
        retry_count=$((retry_count + 1))
        log_info "等待服務健康... ($retry_count/$HEALTH_CHECK_RETRIES)"
        sleep $HEALTH_CHECK_INTERVAL
    done
    
    log_error "服務健康檢查超時"
    return 1
}

# 測試後端 API
test_backend_api() {
    log_info "🧪 測試後端 API..."
    
    local backend_url="http://localhost:8001"
    local api_endpoints=(
        "/api/v1/health/"
        "/api/v1/"
        "/admin/"
    )
    
    # 等待後端服務完全啟動
    local retry_count=0
    while [[ $retry_count -lt 30 ]]; do
        if curl -f -s "$backend_url/api/v1/health/" > /dev/null 2>&1; then
            break
        fi
        retry_count=$((retry_count + 1))
        log_info "等待後端 API 啟動... ($retry_count/30)"
        sleep 5
    done
    
    # 測試各個端點
    for endpoint in "${api_endpoints[@]}"; do
        log_info "測試端點: $endpoint"
        
        local response_code
        response_code=$(curl -s -o /dev/null -w "%{http_code}" "$backend_url$endpoint" || echo "000")
        
        if [[ "$response_code" =~ ^[23] ]]; then
            log_success "端點 $endpoint 響應正常 (HTTP $response_code)"
        else
            log_error "端點 $endpoint 響應異常 (HTTP $response_code)"
            return 1
        fi
    done
    
    log_success "後端 API 測試通過"
    return 0
}

# 測試資料庫連接
test_database_connection() {
    log_info "🗄️ 測試資料庫連接..."
    
    # 執行 Django 管理命令測試資料庫
    if docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" exec -T backend-ci bash -c "cd backend && python manage.py check --database default"; then
        log_success "資料庫連接測試通過"
    else
        log_error "資料庫連接測試失敗"
        return 1
    fi
    
    return 0
}

# 測試 Redis 連接
test_redis_connection() {
    log_info "🔴 測試 Redis 連接..."
    
    # 測試 Redis 連接
    if docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" exec -T redis-ci redis-cli ping | grep -q "PONG"; then
        log_success "Redis 連接測試通過"
    else
        log_error "Redis 連接測試失敗"
        return 1
    fi
    
    return 0
}

# 測試 WSGI 應用
test_wsgi_application() {
    log_info "🔥 測試 WSGI 應用..."
    
    # 在容器中測試 WSGI 應用導入
    if docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" exec -T backend-ci bash -c "cd backend && python -c 'from config.wsgi import application; print(\"WSGI application loaded successfully\")'"; then
        log_success "WSGI 應用測試通過"
    else
        log_error "WSGI 應用測試失敗"
        return 1
    fi
    
    return 0
}

# 收集服務日誌
collect_logs() {
    log_info "📋 收集服務日誌..."
    
    local log_dir="/tmp/ci-deployment-logs-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$log_dir"
    
    local services=("postgres-ci" "redis-ci" "backend-ci")
    
    for service in "${services[@]}"; do
        log_info "收集 $service 日誌..."
        docker-compose -f "$COMPOSE_FILE" -p "$COMPOSE_PROJECT" logs "$service" > "$log_dir/$service.log" 2>&1 || true
    done
    
    log_info "日誌已保存到: $log_dir"
    return 0
}

# 主函數
main() {
    local exit_code=0
    
    log_info "🚀 開始 CI 部署模擬測試..."
    echo ""
    
    # 檢查前置條件
    if ! check_prerequisites; then
        log_error "前置條件檢查失敗"
        return 1
    fi
    
    echo ""
    
    # 啟動服務
    if ! start_services; then
        log_error "服務啟動失敗"
        exit_code=1
    fi
    
    echo ""
    
    # 等待服務健康
    if [[ $exit_code -eq 0 ]] && ! wait_for_services; then
        log_error "服務健康檢查失敗"
        exit_code=1
    fi
    
    echo ""
    
    # 測試資料庫連接
    if [[ $exit_code -eq 0 ]] && ! test_database_connection; then
        log_error "資料庫連接測試失敗"
        exit_code=1
    fi
    
    echo ""
    
    # 測試 Redis 連接
    if [[ $exit_code -eq 0 ]] && ! test_redis_connection; then
        log_error "Redis 連接測試失敗"
        exit_code=1
    fi
    
    echo ""
    
    # 測試 WSGI 應用
    if [[ $exit_code -eq 0 ]] && ! test_wsgi_application; then
        log_error "WSGI 應用測試失敗"
        exit_code=1
    fi
    
    echo ""
    
    # 測試後端 API
    if [[ $exit_code -eq 0 ]] && ! test_backend_api; then
        log_error "後端 API 測試失敗"
        exit_code=1
    fi
    
    echo ""
    
    # 收集日誌（無論成功失敗）
    collect_logs
    
    echo ""
    
    if [[ $exit_code -eq 0 ]]; then
        log_success "🎉 CI 部署模擬測試全部通過！"
        log_info "✨ 部署安全網防線三：部署模擬測試 - 正常"
    else
        log_error "💥 CI 部署模擬測試失敗！"
        log_error "🚨 部署安全網防線三：部署模擬測試 - 異常"
        log_info "📝 請檢查以上錯誤信息和日誌"
    fi
    
    return $exit_code
}

# 執行主函數
main "$@"

---
allowed-tools: Read, Grep, Glob, Task, Bash(git: log|status|diff), Bash(gh: api|issue|pr)
description: Task-Dispatcher v2.4 "The Analyst" - 任務分析與規劃專家。接收複雜任務，運行五重子代理分析，生成詳盡的 HTML 分析報告供人類和 Coder Agent 使用。v2.4 核心改進：工作包內整合原子任務完整執行詳情，讓 agent 明確知道具體要完成什麼。
---

## 🚀 Task-Dispatcher v2.4 "The Analyst" - 任務分析與規劃專家

### 💡 核心理念：深度分析與智能規劃

**🔍 v2.4 核心定位**：專業的任務分析專家，為複雜任務提供全面的分析和規劃支援
- **目標讀者**：人類指揮官與 Coder Agent
- **🆕 v2.4 核心價值**：將複雜任務轉化為清晰、可執行的行動方案，工作包內整合原子任務完整執行詳情
- **🆕 v2.4 關鍵改進**：讓領取工作包的 agent 能夠明確知道自己要完成什麼，包含詳細的執行步驟和驗收標準

**v2.4 SOP 標準作業程序**：
1. **📥 接收任務**：接收一個父任務 Issue
2. **🧠 五重分析**：運行完整的「五重子代理」深度分析
3. **📊 生成報告**：產出詳盡的 HTML 分析報告
4. **📢 發布通知**：在父任務下發表規劃完成評論

> **新使命**：成為「任務分析與規劃專家」，將複雜任務轉化為清晰的執行藍圖

**五重子代理分析架構**：
- **🧠 Task Analysis Agent**：從 DoD 推理原子任務清單
- **🔗 Dependency Reasoning Agent**：分析任務間依賴關係
- **📦 Work Package Grouping Agent**：智能聚合邏輯工作包
- **⚡ Optimization Strategy Agent**：提出並行優化策略
- **📚 Context Intelligence Agent**：識別相關檔案和上下文

---

## 🔍 Stage 0：狀態驗證協議 (Pre-Action State Validation)

### 🛡️ 強制前置檢查 - 防止狀態不一致

**AI 指令**: 在執行任何任務分析之前，必須先驗證 Issue 的當前狀態和可處理性：

```bash
#!/bin/bash

# 接收參數
ISSUE_NUMBER=$1
ANALYSIS_MODE=${2:-"report"}  # 預設為 report 模式

if [[ -z "$ISSUE_NUMBER" ]]; then
    echo "❌ 錯誤：請提供 Issue 編號"
    echo "使用方式: /task-dispatcher.md <ISSUE_NUMBER> [codex]"
    echo ""
    echo "模式說明："
    echo "  預設模式 - 生成詳盡的 HTML 分析報告，供人類指揮官和 Coder Agent 使用"
    echo "  codex   - 生成結構化工作包評論（舊版相容模式）"
    exit 1
fi

# 驗證並設置分析模式
if [[ "$ANALYSIS_MODE" == "codex" ]]; then
    echo "🏗️ 分析模式：Codex（結構化工作包）"
    echo "📋 目標：生成可執行的工作包評論"
else
    echo "📊 分析模式：Report（詳盡分析報告）- 預設"
    echo "📋 目標：生成全面的 HTML 分析報告"
fi

echo "🚀 Task-Dispatcher v2.4 啟動 - 任務分析與規劃專家"
echo "🎯 分析目標：Issue #$ISSUE_NUMBER"

echo "🚀 Task-Dispatcher 啟動 - AI 推理驅動的任務分派引擎"
echo "🎯 目標 Issue：#$ISSUE_NUMBER"

# === 🔍 狀態驗證協議 ===
echo "🔍 執行 Stage 0：Issue 狀態驗證..."

# 檢查 Issue 是否存在
if ! gh issue view $ISSUE_NUMBER >/dev/null 2>&1; then
    echo "❌ 狀態驗證失敗：Issue #$ISSUE_NUMBER 不存在"
    echo "🔍 請確認 Issue 編號是否正確"
    exit 1
fi

# 檢查 Issue 當前狀態
CURRENT_STATE=$(gh issue view $ISSUE_NUMBER --json state --jq '.state')

if [ "$CURRENT_STATE" != "OPEN" ]; then
    echo "❌ 狀態驗證失敗：Issue #$ISSUE_NUMBER 狀態為 $CURRENT_STATE，與預期 OPEN 不符"
    echo "🔍 可能原因：已被手動處理 (關閉/完成)"
    echo "💡 建議：重新評估任務清單或檢查 Issue 狀態"
    exit 1
fi

# 檢查 Issue 是否已有關聯的 PR
LINKED_PRS=$(gh issue view $ISSUE_NUMBER --json timelineItems --jq '.timelineItems[] | select(.typename == "CrossReferencedEvent") | .source.number' 2>/dev/null || echo "")
if [ -n "$LINKED_PRS" ]; then
    echo "ℹ️ 檢測到關聯 PR：$LINKED_PRS"
    echo "🔍 可能需要檢查是否已有進行中的實作工作"
fi

echo "✅ 狀態驗證通過：Issue #$ISSUE_NUMBER 處於可處理狀態"
echo "📊 Issue 詳情：狀態=$CURRENT_STATE"
echo ""
```

---

## 🧩 第一階段：智能任務推理 (Intelligent Task Reasoning)

### 1. 任務輸入與上下文準備

狀態驗證通過後，開始進行 AI 驅動的任務分析：

```bash
if [[ -z "$ISSUE_NUMBER" ]]; then
    echo "❌ 錯誤：請提供 Issue 編號"
    echo "使用方式: /task-dispatcher.md 127"
    exit 1
fi

echo "🚀 Task-Dispatcher AI 推理引擎啟動"
echo "🧠 啟動四重子代理協作模式..."

# 設置報告路徑
REPORT_DATE=$(date +'%Y-%m-%d')
REPORT_DIR="docs/04_AI_OPERATIONS/task-dispatcher"
HTML_REPORT="$REPORT_DIR/Issue-${ISSUE_NUMBER}-dispatch-report.html"
mkdir -p "$REPORT_DIR"

# 獲取 Issue 完整內容
ISSUE_JSON=$(gh issue view $ISSUE_NUMBER --json title,body,labels,comments)
ISSUE_TITLE=$(echo "$ISSUE_JSON" | jq -r .title)
ISSUE_BODY=$(echo "$ISSUE_JSON" | jq -r .body)
ISSUE_LABELS=$(echo "$ISSUE_JSON" | jq -r '.labels[].name' | tr '\n' ',' | sed 's/,$//')

# 🆕 v2.3: 讀取 Navigator 提供的工作量評估
ESTIMATED_EFFORT_SCALE=$(echo "$ISSUE_LABELS" | grep -o 'effort:[^,]*' | cut -d: -f2)
if [[ -z "$ESTIMATED_EFFORT_SCALE" ]]; then
    ESTIMATED_EFFORT_SCALE="medium" # 默認值
    echo "⚠️ 未檢測到 effort 標籤，使用默認值: medium"
else
    echo "⚖️ Navigator 工作量評估: effort:$ESTIMATED_EFFORT_SCALE"
fi

echo "📋 分析目標：$ISSUE_TITLE"
echo "🏷️ Issue 標籤：$ISSUE_LABELS"
echo "📊 工作量感知模式：已啟用 (v2.3)"

# 提取並驗證 DoD
extract_and_validate_dod
```

### 2. DoD 提取與驗證
```bash
function extract_and_validate_dod() {
    echo "🔍 提取並驗證 DoD..."

    # 提取 DoD 區塊
    DOD_SECTION=$(echo "$ISSUE_BODY" | sed -n '/### ✅ 驗收標準/,/###/p' | head -n -1)

    if [[ -z "$DOD_SECTION" ]]; then
        echo "⚠️ 未找到明確的 DoD，啟動智能推理模式..."
        AI_infer_dod_from_issue
    else
        echo "✅ 發現結構化 DoD，進行驗證..."
        AI_validate_and_enhance_dod
    fi
}

function AI_infer_dod_from_issue() {
    echo "🧠 Task Analysis Agent：從 Issue 描述推理 DoD..."

    # AI 推理指令：從 Issue 推理 DoD
    cat > /tmp/dod_inference_prompt.txt <<EOF
你是一位資深的產品經理和技術架構師。請基於以下 Issue 信息，推理出完整的驗收標準 (Definition of Done)。

**Issue 標題**: $ISSUE_TITLE
**Issue 內容**:
$ISSUE_BODY

**任務要求**:
1. 仔細分析 Issue 的目標和範圍
2. 推理出實現這個目標所需的所有關鍵驗收點
3. 每個驗收點都要具體、可測量、可驗證
4. 輸出格式為 Markdown 檢查清單

**輸出格式**:
- [ ] **驗收點1**: 具體描述
- [ ] **驗收點2**: 具體描述
...

請開始推理：
EOF

    # 執行 AI 推理（模擬）
    echo "🤖 執行 AI 推理..."
    AI_INFERRED_DOD=$(generate_ai_dod_inference)

    DOD_SECTION="$AI_INFERRED_DOD"
    echo "✅ AI 推理完成，生成了 DoD"
}

function AI_validate_and_enhance_dod() {
    echo "🧠 Task Analysis Agent：驗證並增強現有 DoD..."

    # AI 驗證指令
    cat > /tmp/dod_validation_prompt.txt <<EOF
你是一位資深的 QA 總監。請審查以下 DoD，識別遺漏或不夠明確的部分，並提出改進建議。

**現有 DoD**:
$DOD_SECTION

**驗證標準**:
1. 是否涵蓋了功能正確性？
2. 是否涵蓋了測試要求？
3. 是否涵蓋了集成和部署？
4. 是否涵蓋了文檔更新？
5. 每個檢查點是否具體可驗證？

**輸出**:
- 改進後的完整 DoD（Markdown 格式）
- 改進說明

請開始驗證：
EOF

    echo "🤖 執行 AI 驗證..."
    VALIDATED_DOD=$(validate_existing_dod)

    DOD_SECTION="$VALIDATED_DOD"
    echo "✅ DoD 驗證並增強完成"
}
```

---

## 🧠 第二階段：智能任務分解與聚合 (Intelligent Task Decomposition & Aggregation)

### 1. 動態原子任務推理
```bash
function dynamic_atomic_task_analysis() {
    echo "🧠 Task Analysis Agent：執行動態原子任務推理..."

    # AI 推理指令：完全動態的任務分解
    cat > /tmp/atomic_task_prompt.txt <<EOF
你是一位資深的技術架構師和項目經理。請忽略所有預設的、針對特定類型的任務列表。

**你的唯一信息來源**：
$DOD_SECTION

**核心任務**：
對於 DoD 中的每一項驗收標準，請將其分解為一個或多個為了實現它所必需的、最小化的「原子開發任務」。

**要求**：
1. 每個原子任務必須是獨立、可驗證的最小工作單元
2. 確保 100% 覆蓋所有 DoD 標準
3. 任務描述必須是動詞開頭的具體行動
4. 避免過度拆分（每個任務應該是有意義的完整工作）
5. **強制性 TDD 要求**：每個實現任務都必須包含對應的單元測試任務
6. 測試任務應遵循 TDD 紅綠重構循環

**輸出格式**（JSON）：
{
  "atomic_tasks": [
    {
      "id": "TASK_1",
      "description": "具體的開發任務描述",
      "covers_dod": ["相關的DoD檢查點"],
      "estimated_effort": "預估工作量（如：1-2天）",
      "category": "推理出的任務類別（如：實現、測試、配置等）"
    }
  ],
  "coverage_analysis": "確保所有DoD都被覆蓋的分析說明"
}

請開始推理：
EOF

    echo "🤖 執行深度任務分解推理..."
    ATOMIC_TASKS_JSON=$(execute_atomic_task_reasoning)

    # 解析 JSON 結果
    parse_atomic_tasks_result

    echo "✅ 動態推理完成，識別了 ${#ATOMIC_TASKS[@]} 個原子任務"
}

function execute_atomic_task_reasoning() {
    # 模擬 AI 推理結果（實際實現中可以調用 LLM API）
    cat <<'EOF'
{
  "atomic_tasks": [
    {
      "id": "TASK_1",
      "description": "設計並實現數據模型和數據庫schema",
      "covers_dod": ["數據完整性", "數據模型一致性"],
      "estimated_effort": "1-2天",
      "category": "基礎設施"
    },
    {
      "id": "TASK_2",
      "description": "實現核心業務邏輯和演算法",
      "covers_dod": ["功能正確性", "業務邏輯準確性"],
      "estimated_effort": "2-3天",
      "category": "核心實現"
    },
    {
      "id": "TASK_3",
      "description": "設計並實現API接口定義",
      "covers_dod": ["接口完整性", "API文檔同步"],
      "estimated_effort": "1天",
      "category": "接口設計"
    },
    {
      "id": "TASK_4",
      "description": "實現前端組件和用戶界面",
      "covers_dod": ["用戶體驗達標", "響應式設計"],
      "estimated_effort": "2-3天",
      "category": "前端實現"
    },
    {
      "id": "TASK_5",
      "description": "建立 TDD 測試框架和單元測試套件",
      "covers_dod": ["TDD 流程建立", "單元測試覆蓋率 ≥ 80%", "測試獨立性"],
      "estimated_effort": "1-2天",
      "category": "TDD測試"
    },
    {
      "id": "TASK_6",
      "description": "實現集成測試和端到端驗證",
      "covers_dod": ["端到端流程驗證", "集成正確性", "CI 測試通過"],
      "estimated_effort": "1-2天",
      "category": "集成測試"
    },
    {
      "id": "TASK_7",
      "description": "配置部署環境和CI/CD流程",
      "covers_dod": ["部署自動化", "環境一致性"],
      "estimated_effort": "1天",
      "category": "部署"
    },
    {
      "id": "TASK_8",
      "description": "更新技術文檔和使用說明",
      "covers_dod": ["文檔完整性", "文檔準確性"],
      "estimated_effort": "0.5-1天",
      "category": "文檔"
    }
  ],
  "coverage_analysis": "所有 DoD 檢查點均已被原子任務覆蓋，確保100%完成度"
}
EOF
}

function parse_atomic_tasks_result() {
    # 解析 JSON 並存儲到數組
    ATOMIC_TASKS=()
    ATOMIC_TASKS_MAP=()  # 清空哈希表

    # 使用 jq 解析（簡化版本）
    local task_count=$(echo "$ATOMIC_TASKS_JSON" | jq '.atomic_tasks | length')

    for ((i=0; i<task_count; i++)); do
        local task_id=$(echo "$ATOMIC_TASKS_JSON" | jq -r ".atomic_tasks[$i].id")
        local task_desc=$(echo "$ATOMIC_TASKS_JSON" | jq -r ".atomic_tasks[$i].description")
        local task_category=$(echo "$ATOMIC_TASKS_JSON" | jq -r ".atomic_tasks[$i].category")
        local task_effort=$(echo "$ATOMIC_TASKS_JSON" | jq -r ".atomic_tasks[$i].estimated_effort")

        local task_entry="$task_id:$task_desc:$task_category:$task_effort"
        ATOMIC_TASKS+=("$task_entry")
        # 建立哈希表以優化查找性能
        ATOMIC_TASKS_MAP[$task_id]="$task_entry"
    done

    echo "📊 任務分解結果："
    for task in "${ATOMIC_TASKS[@]}"; do
        local parts=($(echo $task | tr ':' '\n'))
        echo "   ${parts[0]}: ${parts[1]} (${parts[2]}, ${parts[3]})"
    done
}

# 🆕 優化的任務查找函數
function get_task_info_by_id() {
    local task_id="$1"
    local field="$2"  # desc, category, effort

    if [[ -n "${ATOMIC_TASKS_MAP[$task_id]}" ]]; then
        local task="${ATOMIC_TASKS_MAP[$task_id]}"
        case "$field" in
            "desc") echo "$task" | cut -d: -f2 ;;
            "category") echo "$task" | cut -d: -f3 ;;
            "effort") echo "$task" | cut -d: -f4 ;;
            *) echo "$task" ;;
        esac
    fi
}

# 📝 v2.4 清理: 移除廢棄的計算函數
# - calculate_number_of_parallel_tracks()
# - calculate_efficiency_improvement()
# 原因: 這些函數未被主流程調用，屬於遺留代碼
```

### 2. AI 驅動的依賴推理
```bash
function AI_dependency_reasoning() {
    echo "🔗 Dependency Reasoning Agent：執行深度依賴推理..."

    # 準備任務信息給 AI
    local tasks_info=""
    for task in "${ATOMIC_TASKS[@]}"; do
        local parts=($(echo $task | tr ':' '\n'))
        tasks_info="${tasks_info}${parts[0]}: ${parts[1]} (類別: ${parts[2]})\n"
    done

    # AI 推理指令：深度依賴分析
    cat > /tmp/dependency_reasoning_prompt.txt <<EOF
你是一位資深的系統設計師和架構師。請分析以下原子任務之間的邏輯依賴關係。

**原子任務列表**：
$tasks_info

**分析要求**：
1. 扮演系統設計師，深度思考每個任務的邏輯前置條件
2. 不要基於關鍵字匹配，而要基於真實的技術依賴關係
3. 區分「硬依賴」（必須等待）和「軟依賴」（可通過工程實踐解耦）
4. 為每個依賴關係提供清晰的技術理由

**輸出格式**（JSON）：
{
  "dependencies": {
    "TASK_ID": {
      "hard_dependencies": ["必須等待的任務ID"],
      "soft_dependencies": ["可解耦的任務ID"],
      "reasoning": "依賴關係的技術原因說明"
    }
  },
  "dependency_analysis": "整體依賴分析和關鍵路徑識別"
}

請基於技術邏輯進行深度推理：
EOF

    echo "🤖 執行依賴關係深度推理..."
    DEPENDENCY_JSON=$(execute_dependency_reasoning)

    # 解析依賴結果
    parse_dependency_result

    echo "✅ 依賴推理完成"
}

function execute_dependency_reasoning() {
    # 模擬 AI 深度推理結果
    cat <<'EOF'
{
  "dependencies": {
    "TASK_1": {
      "hard_dependencies": [],
      "soft_dependencies": [],
      "reasoning": "數據模型設計是基礎，無前置依賴"
    },
    "TASK_2": {
      "hard_dependencies": ["TASK_1"],
      "soft_dependencies": [],
      "reasoning": "核心業務邏輯需要明確的數據模型才能實現"
    },
    "TASK_3": {
      "hard_dependencies": [],
      "soft_dependencies": ["TASK_2"],
      "reasoning": "API接口設計可以基於需求分析並行進行，但與業務邏輯有軟耦合"
    },
    "TASK_4": {
      "hard_dependencies": ["TASK_3"],
      "soft_dependencies": [],
      "reasoning": "前端實現需要明確的API接口定義"
    },
    "TASK_5": {
      "hard_dependencies": [],
      "soft_dependencies": ["TASK_2", "TASK_3"],
      "reasoning": "測試框架可以並行搭建，但測試用例編寫需要接口和邏輯定義"
    },
    "TASK_6": {
      "hard_dependencies": ["TASK_2", "TASK_4"],
      "soft_dependencies": [],
      "reasoning": "集成測試需要後端邏輯和前端組件都實現完成"
    },
    "TASK_7": {
      "hard_dependencies": [],
      "soft_dependencies": ["TASK_1"],
      "reasoning": "部署環境可以並行準備，但需要了解數據模型要求"
    },
    "TASK_8": {
      "hard_dependencies": [],
      "soft_dependencies": ["TASK_3"],
      "reasoning": "文檔可以基於設計先行編寫，但API文檔需要接口定義"
    }
  },
  "dependency_analysis": "關鍵路徑：TASK_1 → TASK_2 → TASK_6，長度為3。多個任務可以並行執行，特別是TASK_3、TASK_5、TASK_7、TASK_8在早期階段就可以開始。"
}
EOF
}

function parse_dependency_result() {
    declare -g -A HARD_DEPENDENCIES
    declare -g -A SOFT_DEPENDENCIES
    declare -g -A DEPENDENCY_REASONING

    # 解析每個任務的依賴
    for task in "${ATOMIC_TASKS[@]}"; do
        local task_id=$(echo $task | cut -d: -f1)

        # 提取硬依賴
        local hard_deps=$(echo "$DEPENDENCY_JSON" | jq -r ".dependencies.${task_id}.hard_dependencies[]" 2>/dev/null | tr '\n' ',' | sed 's/,$//')
        HARD_DEPENDENCIES[$task_id]="$hard_deps"

        # 提取軟依賴
        local soft_deps=$(echo "$DEPENDENCY_JSON" | jq -r ".dependencies.${task_id}.soft_dependencies[]" 2>/dev/null | tr '\n' ',' | sed 's/,$//')
        SOFT_DEPENDENCIES[$task_id]="$soft_deps"

        # 提取推理過程
        local reasoning=$(echo "$DEPENDENCY_JSON" | jq -r ".dependencies.${task_id}.reasoning" 2>/dev/null)
        DEPENDENCY_REASONING[$task_id]="$reasoning"
    done

    # 輸出依賴分析結果
    echo "📊 依賴推理結果："
    for task_id in "${!HARD_DEPENDENCIES[@]}"; do
        echo "   $task_id:"
        echo "     硬依賴: ${HARD_DEPENDENCIES[$task_id]:-無}"
        echo "     軟依賴: ${SOFT_DEPENDENCIES[$task_id]:-無}"
        echo "     原因: ${DEPENDENCY_REASONING[$task_id]}"
    done
}
```

### 3. 🚀 **NEW** - Work Package Grouping Agent（關鍵新增）
```bash
function AI_work_package_grouping() {
    echo "📦 Work Package Grouping Agent：智能聚合工作包..."

    # 準備原子任務和依賴信息
    local tasks_and_deps=""
    for task in "${ATOMIC_TASKS[@]}"; do
        local task_id=$(echo $task | cut -d: -f1)
        local task_desc=$(echo $task | cut -d: -f2)
        local task_category=$(echo $task | cut -d: -f3)
        local task_effort=$(echo $task | cut -d: -f4)
        local hard_deps="${HARD_DEPENDENCIES[$task_id]:-無}"

        tasks_and_deps="${tasks_and_deps}$task_id: $task_desc (類別: $task_category, 工作量: $task_effort, 硬依賴: $hard_deps)\n"
    done

    # 準備包含工作量信息的任務列表
    local tasks_and_deps_with_effort=""
    local total_estimated_hours=0

    for task in "${ATOMIC_TASKS[@]}"; do
        local task_id=$(echo $task | cut -d: -f1)
        local task_desc=$(echo $task | cut -d: -f2)
        local task_category=$(echo $task | cut -d: -f3)
        local task_effort=$(echo $task | cut -d: -f4)
        local hard_deps="${HARD_DEPENDENCIES[$task_id]:-無}"

        # 使用正則表達式提取工時數值，提高魯棒性
        local hours=$(echo "$task_effort" | grep -oE '[0-9]+(\.[0-9]+)?' | head -1)
        # 檢查是否成功提取到數值
        if [[ -n "$hours" ]]; then
            # 將提取到的數值轉換為整數並累加
            total_estimated_hours=$((total_estimated_hours + ${hours%.*}))
        else
            # 如果提取失敗，則使用默認值
            total_estimated_hours=$((total_estimated_hours + 4))
        fi

        tasks_and_deps_with_effort="${tasks_and_deps_with_effort}$task_id: $task_desc (類別: $task_category, 工作量: $task_effort, 硬依賴: $hard_deps)\n"
    done

    # 🆕 v2.4: AI 工作包聚合指令 (原子任務詳情整合版本)
    cat > /tmp/work_package_grouping_prompt.txt <<EOF
你是一位資深的敏捷開發項目經理和「工頭」。請將以下原子任務，根據其邏輯、依賴和**工作量**，聚合成一系列合理的工作包。

**🆕 v2.4 核心改進**: 工作包必須包含原子任務的完整執行詳情，讓領取工作包的 agent 能夠明確知道自己要完成什麼。

**🆕 總體任務規模**: $ESTIMATED_EFFORT_SCALE (來自 Navigator 評估)
**🆕 原子任務總工時**: 約 $total_estimated_hours 小時

**原子任務列表 (含估算工時)**：
$tasks_and_deps_with_effort

**🆕 聚合原則與強約束 (v2.4 原子任務詳情整合)**：
1. **工作包規模約束** (智能分包引擎):
   - **IF** 總體任務規模是 'small' (<4h), **THEN** 聚合為 **1-2個** 工作包，避免管理開銷
   - **IF** 總體任務規模是 'medium' (4h-2d), **THEN** 聚合為 **2-4個** 工作包，平衡並行度
   - **IF** 總體任務規模是 'large' (>2d), **THEN** 聚合為 **4-6個** 工作包，並考慮創建 **子史詩 (Sub-Epic)** 進行二級管理

2. **邏輯相關性優先**：相關功能的任務應聚合在同一工作包
3. **依賴最小化**：盡量將無依賴或可並行的任務打包在一起
4. **技術棧一致性**：前後端、CI、測試等盡量分開聚合
5. **單一職責**：每個工作包都應有一個清晰、單一的目標
6. **🆕 原子任務詳情完整性**：每個工作包必須包含原子任務的完整執行步驟和驗收標準
7. **🆕 工程實踐約束**：每個工作包的工作量應在 1-4 小時範圍內，適合 Coder Agent 在一個工作段完成

**🆕 v2.4 輸出格式**（包含原子任務詳情）：
{
  "work_packages": [
    {
      "id": "WP-01",
      "title": "具體的工作包標題（如：修復安全依賴並本地驗證）",
      "priority": "P0/P1/P2",
      "estimated_time": "預估時間（如：1-2小時）",
      "prerequisite_packages": ["前置工作包ID"],
      "can_start_immediately": true/false,
      "技術棧": "前端/後端/測試/配置等",
      "聚合理由": "為什麼這些任務應該聚合在一起的具體原因",
      "atomic_tasks": [
        {
          "id": "A1",
          "title": "原子任務標題",
          "description": "詳細描述此任務要完成什麼",
          "action_steps": [
            "具體執行步驟1",
            "具體執行步驟2",
            "具體執行步驟3"
          ],
          "expected_files": ["預期會修改或創建的檔案"],
          "acceptance_criteria": [
            "驗收標準1",
            "驗收標準2"
          ],
          "estimated_effort": "工作量估算"
        }
      ],
      "execution_context": {
        "related_files": ["相關檔案清單"],
        "environment_setup": "環境設定要求",
        "common_pitfalls": ["常見陷阱和注意事項"],
        "testing_requirements": "測試要求"
      }
    }
  ],
  "parallel_execution_plan": {
    "phase_1_packages": ["可立即並行開始的工作包ID"],
    "phase_2_packages": ["第二階段工作包ID"],
    "phase_3_packages": ["最終收尾工作包ID"]
  },
  "efficiency_analysis": "聚合後的並行效率分析和預期改善"
}

請進行智能工作包聚合（v2.4 原子任務詳情整合版本）：
EOF

    echo "🤖 執行工作包聚合推理..."
    WORK_PACKAGES_JSON=$(execute_work_package_grouping)

    # 解析工作包結果
    parse_work_packages_result

    echo "✅ 工作包聚合完成，生成了 ${#WORK_PACKAGES[@]} 個工作包"
}

function execute_work_package_grouping() {
    # 🆕 v2.3: 根據工作量規模動態生成聚合結果
    case "$ESTIMATED_EFFORT_SCALE" in
        "small")
            # Small 任務：1-2個工作包 (v2.4 原子任務詳情整合版本)
            cat <<'EOF'
{
  "work_packages": [
    {
      "id": "WP-01",
      "title": "安全依賴修復 (小型任務)",
      "priority": "P0",
      "estimated_time": "2-3小時",
      "prerequisite_packages": [],
      "can_start_immediately": true,
      "技術棧": "前端依賴管理",
      "聚合理由": "Small規模任務，避免過度拆分，將相關任務聚合為單一工作包",
      "atomic_tasks": [
        {
          "id": "A1",
          "title": "更新高危依賴套件",
          "description": "使用 pnpm 更新所有高危安全依賴套件到最新穩定版本",
          "action_steps": [
            "執行 pnpm audit 檢查當前安全漏洞",
            "執行 pnpm update 更新所有依賴套件",
            "檢查 pnpm-lock.yaml 是否正確更新",
            "執行 pnpm audit 確認漏洞已修復"
          ],
          "expected_files": ["pnpm-lock.yaml", "package.json"],
          "acceptance_criteria": [
            "pnpm audit 不再顯示高危或中危漏洞",
            "所有依賴版本已更新到安全版本",
            "pnpm-lock.yaml 已正確生成"
          ],
          "estimated_effort": "30分鐘"
        },
        {
          "id": "A2",
          "title": "驗證依賴更新後的建置",
          "description": "確保依賴更新後專案能正常建置和執行",
          "action_steps": [
            "執行 pnpm install 重新安裝依賴",
            "執行 pnpm run build 測試建置",
            "執行 pnpm run dev 測試開發環境",
            "檢查控制台是否有錯誤訊息"
          ],
          "expected_files": ["dist/", "build/"],
          "acceptance_criteria": [
            "建置過程無錯誤",
            "開發伺服器正常啟動",
            "前端頁面正常載入"
          ],
          "estimated_effort": "45分鐘"
        },
        {
          "id": "A5a",
          "title": "本地功能驗證測試",
          "description": "手動測試關鍵功能確保依賴更新未破壞現有功能",
          "action_steps": [
            "啟動本地開發環境",
            "測試主要頁面載入",
            "測試基本功能操作",
            "檢查網路請求是否正常"
          ],
          "expected_files": [],
          "acceptance_criteria": [
            "所有主要頁面正常載入",
            "核心功能運作正常",
            "無明顯錯誤或警告"
          ],
          "estimated_effort": "60分鐘"
        }
      ],
      "execution_context": {
        "related_files": ["package.json", "pnpm-lock.yaml", "Makefile"],
        "environment_setup": "確保使用 pnpm 而非 npm 進行套件管理",
        "common_pitfalls": [
          "避免使用 npm install，務必使用 pnpm",
          "注意檢查 lock 檔案變更是否合理",
          "確保更新後的套件版本相容"
        ],
        "testing_requirements": "完成後執行 make ci-check 確保所有檢查通過"
      }
    }
  ],
  "parallel_execution_plan": {
    "phase_1_packages": ["WP-01"],
    "phase_2_packages": [],
    "phase_3_packages": []
  },
  "efficiency_analysis": "Small規模任務採用最小分包策略，避免管理開銷。單一工作包包含完整的執行步驟和驗收標準，可在一個工作時段內完成，效率最優。"
}
EOF
            ;;
        "large")
            # Large 任務：4-6個工作包 (v2.4 原子任務詳情整合版本)
            cat <<'EOF'
{
  "work_packages": [
    {
      "id": "WP-01",
      "title": "核心依賴與安全修復",
      "priority": "P0",
      "estimated_time": "1-2小時",
      "prerequisite_packages": [],
      "can_start_immediately": true,
      "技術棧": "前端依賴管理",
      "聚合理由": "Large規模任務的核心修復部分，需要優先處理"
    },
    {
      "id": "WP-02",
      "title": "CI安全監控增強",
      "atomic_tasks": ["A4"],
      "priority": "P1",
      "estimated_time": "1-1.5小時",
      "prerequisite_packages": [],
      "can_start_immediately": true,
      "技術棧": "CI/CD基礎設施",
      "聚合理由": "獨立的基礎設施改進，可並行進行"
    },
    {
      "id": "WP-03",
      "title": "本地驗證與測試",
      "atomic_tasks": ["A5a"],
      "priority": "P1",
      "estimated_time": "0.5-1小時",
      "prerequisite_packages": ["WP-01"],
      "can_start_immediately": false,
      "技術棧": "測試驗證",
      "聚合理由": "Large任務需要獨立的驗證階段"
    },
    {
      "id": "WP-04",
      "title": "外部服務狀態同步",
      "atomic_tasks": ["A3"],
      "priority": "P2",
      "estimated_time": "0.5-1小時",
      "prerequisite_packages": ["WP-01"],
      "can_start_immediately": false,
      "技術棧": "外部集成",
      "聚合理由": "Large任務的外部集成部分，需要等待核心修復"
    },
    {
      "id": "WP-05",
      "title": "文檔更新與知識管理",
      "atomic_tasks": ["A6", "A5b"],
      "priority": "P2",
      "estimated_time": "1-1.5小時",
      "prerequisite_packages": ["WP-01", "WP-03"],
      "can_start_immediately": false,
      "技術棧": "文檔管理",
      "聚合理由": "Large任務的文檔化，需要了解完整修復結果"
    }
  ],
  "parallel_execution_plan": {
    "phase_1_packages": ["WP-01", "WP-02"],
    "phase_2_packages": ["WP-03", "WP-04"],
    "phase_3_packages": ["WP-05"]
  },
  "efficiency_analysis": "Large規模任務採用多階段並行策略，最大化並行度。預計從純序列的8-10小時縮短為4-5小時，效率提升50%以上。建議考慮子史詩管理。"
}
EOF
            ;;
        *)
            # Medium 任務：2-4個工作包 (默認，與原來類似)
            cat <<'EOF'
{
  "work_packages": [
    {
      "id": "WP-01",
      "title": "修復安全依賴並本地驗證",
      "atomic_tasks": ["A1", "A2", "A5a"],
      "priority": "P0",
      "estimated_time": "1.5-2小時",
      "prerequisite_packages": [],
      "can_start_immediately": true,
      "技術棧": "前端依賴管理",
      "聚合理由": "Medium規模任務的核心修復流程，聚合相關任務提升效率"
    },
    {
      "id": "WP-02",
      "title": "增強CI流程自動安全監控",
      "atomic_tasks": ["A4"],
      "priority": "P1",
      "estimated_time": "1-1.5小時",
      "prerequisite_packages": [],
      "can_start_immediately": true,
      "技術棧": "CI/CD基礎設施",
      "聚合理由": "獨立的基礎設施改進，適合並行開發"
    },
    {
      "id": "WP-03",
      "title": "外部服務同步與文檔更新",
      "atomic_tasks": ["A3", "A6", "A5b"],
      "priority": "P2",
      "estimated_time": "1-1.5小時",
      "prerequisite_packages": ["WP-01"],
      "can_start_immediately": false,
      "技術棧": "外部集成與文檔",
      "聚合理由": "Medium任務的收尾工作，需要等待核心修復完成"
    }
  ],
  "parallel_execution_plan": {
    "phase_1_packages": ["WP-01", "WP-02"],
    "phase_2_packages": ["WP-03"],
    "phase_3_packages": []
  },
  "efficiency_analysis": "Medium規模任務採用平衡的並行策略。預計從序列執行的4-5小時縮短為2.5-3小時，效率提升25-40%。管理開銷適中，適合標準並行開發。"
}
EOF
            ;;
    esac
}

function parse_work_packages_result() {
    declare -g -a WORK_PACKAGES
    declare -g -A WORK_PACKAGE_DETAILS

    WORK_PACKAGES=()

    # 解析工作包數量 (v2.4 原子任務詳情整合版本)
    local package_count=$(echo "$WORK_PACKAGES_JSON" | jq '.work_packages | length')

    for ((i=0; i<package_count; i++)); do
        local wp_id=$(echo "$WORK_PACKAGES_JSON" | jq -r ".work_packages[$i].id")
        local wp_title=$(echo "$WORK_PACKAGES_JSON" | jq -r ".work_packages[$i].title")
        local wp_priority=$(echo "$WORK_PACKAGES_JSON" | jq -r ".work_packages[$i].priority")
        local wp_time=$(echo "$WORK_PACKAGES_JSON" | jq -r ".work_packages[$i].estimated_time")
        
        # 🆕 v2.4: 解析原子任務詳情
        local wp_atomic_tasks_json=$(echo "$WORK_PACKAGES_JSON" | jq -c ".work_packages[$i].atomic_tasks")
        local wp_tasks=$(echo "$WORK_PACKAGES_JSON" | jq -r ".work_packages[$i].atomic_tasks[].id" 2>/dev/null | tr '\n' ',' | sed 's/,$//' || echo "$WORK_PACKAGES_JSON" | jq -r ".work_packages[$i].atomic_tasks[]" | tr '\n' ',' | sed 's/,$//')
        
        local wp_can_start=$(echo "$WORK_PACKAGES_JSON" | jq -r ".work_packages[$i].can_start_immediately")
        local wp_tech_stack=$(echo "$WORK_PACKAGES_JSON" | jq -r ".work_packages[$i].技術棧")
        local wp_reason=$(echo "$WORK_PACKAGES_JSON" | jq -r ".work_packages[$i].聚合理由")
        
        # 🆕 v2.4: 解析執行上下文
        local wp_execution_context=$(echo "$WORK_PACKAGES_JSON" | jq -c ".work_packages[$i].execution_context" 2>/dev/null || echo "{}")

        WORK_PACKAGES+=("$wp_id")
        # 🆕 v2.4: 儲存原子任務詳情和執行上下文
        WORK_PACKAGE_DETAILS[$wp_id]="title:$wp_title|priority:$wp_priority|time:$wp_time|tasks:$wp_tasks|can_start:$wp_can_start|tech:$wp_tech_stack|reason:$wp_reason|atomic_tasks_json:$wp_atomic_tasks_json|execution_context:$wp_execution_context"
    done

    # 解析並行執行計劃
    PHASE_1_PACKAGES=$(echo "$WORK_PACKAGES_JSON" | jq -r '.parallel_execution_plan.phase_1_packages[]' | tr '\n' ',' | sed 's/,$//')
    PHASE_2_PACKAGES=$(echo "$WORK_PACKAGES_JSON" | jq -r '.parallel_execution_plan.phase_2_packages[]' | tr '\n' ',' | sed 's/,$//')
    PHASE_3_PACKAGES=$(echo "$WORK_PACKAGES_JSON" | jq -r '.parallel_execution_plan.phase_3_packages[]' | tr '\n' ',' | sed 's/,$//')
    EFFICIENCY_ANALYSIS=$(echo "$WORK_PACKAGES_JSON" | jq -r '.efficiency_analysis')

    echo "📊 工作包聚合結果："
    for wp_id in "${WORK_PACKAGES[@]}"; do
        local details="${WORK_PACKAGE_DETAILS[$wp_id]}"
        local title=$(echo "$details" | cut -d'|' -f1 | sed 's/title://')
        local priority=$(echo "$details" | cut -d'|' -f2 | sed 's/priority://')
        local time=$(echo "$details" | cut -d'|' -f3 | sed 's/time://')
        echo "   $wp_id: $title ($priority, $time)"
    done

    echo "⚡ 並行執行計劃："
    echo "   階段1 (並行): $PHASE_1_PACKAGES"
    echo "   階段2 (依序): $PHASE_2_PACKAGES"
    echo "   階段3 (收尾): $PHASE_3_PACKAGES"
    echo "🚀 效率分析: $EFFICIENCY_ANALYSIS"
}
```

---

## ⚡ 第三階段：AI 驅動的優化策略 (AI-Driven Optimization Strategy)

### 1. 創造性解耦策略推理
```bash
function AI_optimization_strategy() {
    echo "⚡ Optimization Strategy Agent：執行創造性優化推理..."

    # 準備當前依賴狀況給 AI
    local current_dependencies=""
    for task_id in "${!HARD_DEPENDENCIES[@]}"; do
        current_dependencies="${current_dependencies}${task_id}: 硬依賴[${HARD_DEPENDENCIES[$task_id]}] 軟依賴[${SOFT_DEPENDENCIES[$task_id]}] 原因: ${DEPENDENCY_REASONING[$task_id]}\n"
    done

    # AI 創造性優化指令
    cat > /tmp/optimization_strategy_prompt.txt <<EOF
你是一位資深的軟體架構師和敏捷開發專家。請分析當前的依賴關係，並提出創造性的解耦策略來最大化並行開發效率。

**當前依賴狀況**：
$current_dependencies

**優化目標**：
1. 識別關鍵路徑（最長的依賴鏈）
2. 提出具體的工程實踐來打破或減少依賴
3. 最大化可並行執行的任務數量

**可用的解耦工程實踐**：
- Interface Definition（接口先行定義）
- Mock Server/Mock Data（模擬數據和服務）
- Feature Flag（功能開關）
- Contract Testing（契約測試）
- Parallel Development Track（並行開發軌道）
- Stub Implementation（桩實現）

**輸出格式**（JSON）：
{
  "critical_path_analysis": {
    "longest_chain": ["任務ID序列"],
    "bottleneck_length": "鏈長度",
    "impact_assessment": "瓶頸影響分析"
  },
  "decoupling_strategies": [
    {
      "target_dependency": "要解耦的依賴關係（如TASK_4依賴TASK_3）",
      "strategy": "具體的工程實踐",
      "implementation": "具體實施方案",
      "benefit": "預期效果",
      "effort": "實施成本評估"
    }
  ],
  "optimized_execution_plan": {
    "phase_1": ["可立即並行開始的任務"],
    "phase_2": ["第二階段並行任務"],
    "phase_3": ["最終整合任務"],
    "parallel_efficiency": "預計並行度提升"
  }
}

請進行深度創造性分析：
EOF

    echo "🤖 執行創造性優化策略推理..."
    OPTIMIZATION_JSON=$(execute_optimization_reasoning)

    # 解析優化策略
    parse_optimization_strategy

    echo "✅ 創造性優化策略推理完成"
}

function execute_optimization_reasoning() {
    # 模擬 AI 創造性推理結果
    cat <<'EOF'
{
  "critical_path_analysis": {
    "longest_chain": ["TASK_1", "TASK_2", "TASK_6"],
    "bottleneck_length": "3階段",
    "impact_assessment": "關鍵路徑長度為3，限制了整體並行效率。TASK_2是最大瓶頸，阻塞了多個下游任務。"
  },
  "decoupling_strategies": [
    {
      "target_dependency": "TASK_4依賴TASK_3（前端依賴API接口）",
      "strategy": "Interface Definition + Mock Server",
      "implementation": "1. 先定義完整的API接口規範 2. 建立Mock Server提供假數據 3. 前端基於Mock並行開發",
      "benefit": "前端和後端可完全並行開發，縮短關鍵路徑1個階段",
      "effort": "0.5天建立Mock環境"
    },
    {
      "target_dependency": "TASK_5軟依賴TASK_2（測試依賴業務邏輯）",
      "strategy": "Contract Testing + Test-Driven Development",
      "implementation": "1. 基於需求定義測試契約 2. 先寫測試框架和測試用例 3. 使用桩實現進行測試",
      "benefit": "測試開發可與邏輯實現並行，提高代碼質量",
      "effort": "0.5天設計測試契約"
    },
    {
      "target_dependency": "TASK_6依賴TASK_2和TASK_4（集成測試依賴實現）",
      "strategy": "Incremental Integration + Feature Flag",
      "implementation": "1. 設計增量集成方案 2. 使用功能開關控制特性上線 3. 逐步啟用和測試功能",
      "benefit": "可以提前進行部分集成測試，降低最終集成風險",
      "effort": "0.5天設計功能開關機制"
    }
  ],
  "optimized_execution_plan": {
    "phase_1": ["TASK_1", "TASK_3", "TASK_5", "TASK_7", "TASK_8"],
    "phase_2": ["TASK_2", "TASK_4"],
    "phase_3": ["TASK_6"],
    "parallel_efficiency": "從原本3階段串行優化為最多5個任務並行執行，預計效率提升60%"
  }
}
EOF
}

function parse_optimization_strategy() {
    # 解析關鍵路徑分析
    CRITICAL_PATH=$(echo "$OPTIMIZATION_JSON" | jq -r '.critical_path_analysis.longest_chain[]' | tr '\n' '→' | sed 's/→$//')
    BOTTLENECK_LENGTH=$(echo "$OPTIMIZATION_JSON" | jq -r '.critical_path_analysis.bottleneck_length')

    echo "🎯 關鍵路徑分析："
    echo "   最長鏈: $CRITICAL_PATH"
    echo "   瓶頸長度: $BOTTLENECK_LENGTH"

    # 解析解耦策略
    echo "💡 創造性解耦策略："
    local strategy_count=$(echo "$OPTIMIZATION_JSON" | jq '.decoupling_strategies | length')
    for ((i=0; i<strategy_count; i++)); do
        local target=$(echo "$OPTIMIZATION_JSON" | jq -r ".decoupling_strategies[$i].target_dependency")
        local strategy=$(echo "$OPTIMIZATION_JSON" | jq -r ".decoupling_strategies[$i].strategy")
        local benefit=$(echo "$OPTIMIZATION_JSON" | jq -r ".decoupling_strategies[$i].benefit")
        echo "   策略$((i+1)): $target → $strategy"
        echo "           效益: $benefit"
    done

    # 解析優化後的執行計劃
    PHASE_1_TASKS=$(echo "$OPTIMIZATION_JSON" | jq -r '.optimized_execution_plan.phase_1[]' | tr '\n' ',' | sed 's/,$//')
    PHASE_2_TASKS=$(echo "$OPTIMIZATION_JSON" | jq -r '.optimized_execution_plan.phase_2[]' | tr '\n' ',' | sed 's/,$//')
    PHASE_3_TASKS=$(echo "$OPTIMIZATION_JSON" | jq -r '.optimized_execution_plan.phase_3[]' | tr '\n' ',' | sed 's/,$//')
    PARALLEL_EFFICIENCY=$(echo "$OPTIMIZATION_JSON" | jq -r '.optimized_execution_plan.parallel_efficiency')

    echo "⚡ 優化後執行計劃："
    echo "   階段1 (並行): $PHASE_1_TASKS"
    echo "   階段2 (並行): $PHASE_2_TASKS"
    echo "   階段3 (收尾): $PHASE_3_TASKS"
    echo "   效率提升: $PARALLEL_EFFICIENCY"
}
```

### 2. 智能上下文檔案識別
```bash
function AI_context_intelligence() {
    echo "📚 Context Intelligence Agent：智能識別相關檔案..."

    # AI 上下文分析指令
    cat > /tmp/context_analysis_prompt.txt <<EOF
你是一位資深的代碼審查專家。請基於以下任務信息，智能識別每個原子任務可能需要閱讀或參考的相關代碼檔案。

**專案資訊**：
- Issue: $ISSUE_TITLE
- 專案標籤: $ISSUE_LABELS

**原子任務列表**：
$(for task in "${ATOMIC_TASKS[@]}"; do
    echo "$task"
done)

**分析要求**：
1. 基於任務描述和專案上下文，推理可能相關的檔案類型和位置
2. 考慮現有代碼結構和通用專案模式
3. 識別接口定義、配置檔案、測試檔案等關鍵依賴
4. 避免過於廣泛，聚焦於直接相關的檔案

**輸出格式**（JSON）：
{
  "contextual_files": {
    "TASK_ID": {
      "interface_files": ["接口定義相關檔案"],
      "implementation_reference": ["實現參考檔案"],
      "test_files": ["測試相關檔案"],
      "config_files": ["配置相關檔案"],
      "documentation": ["文檔檔案"]
    }
  }
}

請進行智能檔案分析：
EOF

    echo "🤖 執行智能上下文分析..."
    CONTEXT_JSON=$(execute_context_analysis)

    # 解析上下文結果
    parse_context_intelligence

    echo "✅ 智能上下文分析完成"
}

function execute_context_analysis() {
    # 模擬 AI 上下文推理結果
    cat <<'EOF'
{
  "contextual_files": {
    "TASK_1": {
      "interface_files": ["backend/models/__init__.py", "backend/database/schema.py"],
      "implementation_reference": ["backend/models/base.py"],
      "test_files": ["backend/tests/test_models.py"],
      "config_files": ["backend/settings/database.py"],
      "documentation": ["docs/database-design.md"]
    },
    "TASK_2": {
      "interface_files": ["backend/services/__init__.py"],
      "implementation_reference": ["backend/services/base_service.py", "backend/utils/"],
      "test_files": ["backend/tests/test_services.py"],
      "config_files": ["backend/settings/business_logic.py"],
      "documentation": ["docs/business-logic.md"]
    },
    "TASK_3": {
      "interface_files": ["backend/api/serializers.py", "backend/api/urls.py"],
      "implementation_reference": ["backend/api/views.py", "backend/api/permissions.py"],
      "test_files": ["backend/tests/test_api.py"],
      "config_files": ["backend/settings/api.py"],
      "documentation": ["docs/api-specification.md"]
    },
    "TASK_4": {
      "interface_files": ["frontend/src/types/", "frontend/src/services/api.ts"],
      "implementation_reference": ["frontend/src/components/", "frontend/src/hooks/"],
      "test_files": ["frontend/src/__tests__/", "frontend/src/components/__tests__/"],
      "config_files": ["frontend/package.json", "frontend/tsconfig.json"],
      "documentation": ["frontend/README.md"]
    }
  }
}
EOF
}

function parse_context_intelligence() {
    declare -g -A CONTEXTUAL_FILES

    for task in "${ATOMIC_TASKS[@]}"; do
        local task_id=$(echo $task | cut -d: -f1)

        # 提取各類檔案
        local interface_files=$(echo "$CONTEXT_JSON" | jq -r ".contextual_files.${task_id}.interface_files[]?" 2>/dev/null | tr '\n' ',' | sed 's/,$//')
        local impl_files=$(echo "$CONTEXT_JSON" | jq -r ".contextual_files.${task_id}.implementation_reference[]?" 2>/dev/null | tr '\n' ',' | sed 's/,$//')
        local test_files=$(echo "$CONTEXT_JSON" | jq -r ".contextual_files.${task_id}.test_files[]?" 2>/dev/null | tr '\n' ',' | sed 's/,$//')

        CONTEXTUAL_FILES[$task_id]="interface:$interface_files|impl:$impl_files|test:$test_files"
    done

    echo "📚 智能上下文檔案識別結果："
    for task_id in "${!CONTEXTUAL_FILES[@]}"; do
        echo "   $task_id: ${CONTEXTUAL_FILES[$task_id]}"
    done
}
```

---

## 🚀 第四階段：工程實踐導向的 PR 簡報生成 (Engineering-Focused PR Brief Generation)

> **核心變革**：不再創建孤兒 Issues，而是在父任務下發表「可執行 PR 簡報」評論

### 1. 生成工作包 PR 簡報
```bash
function generate_work_package_pr_briefs() {
    echo "📋 生成工程實踐導向的 PR 簡報..."

    # 準備簡報內容
    local pr_briefs_content=""
    pr_briefs_content+="<!-- AI_PR_BRIEFS_START -->\n"
    pr_briefs_content+="## 🤖 Task-Dispatcher v2.3 並行分派計劃\n\n"
    pr_briefs_content+="**父任務**: #$ISSUE_NUMBER\n"
    pr_briefs_content+="**生成時間**: $(date +'%Y-%m-%d %H:%M:%S')\n"
    pr_briefs_content+="**總體策略**: 基於 AI 聚合分析，已將任務分解為 **${#WORK_PACKAGES[@]} 個可並行執行的工作包**。\n\n"
    pr_briefs_content+="🎯 **Coder Agent 可以直接領取以下任一簡報來創建 PR**\n\n"
    pr_briefs_content+="---\n\n"

    # 為每個工作包生成簡報
    for wp_id in "${WORK_PACKAGES[@]}"; do
        local details="${WORK_PACKAGE_DETAILS[$wp_id]}"
        local can_start=$(echo "$details" | grep -o 'can_start:[^|]*' | cut -d: -f2)

        if [[ "$can_start" == "true" ]]; then
            pr_briefs_content+="### ✅ 工作包 $wp_id: $(echo "$details" | grep -o 'title:[^|]*' | cut -d: -f2) ($(echo "$details" | grep -o 'priority:[^|]*' | cut -d: -f2))\n\n"
        else
            pr_briefs_content+="### ⏳ 工作包 $wp_id: $(echo "$details" | grep -o 'title:[^|]*' | cut -d: -f2) ($(echo "$details" | grep -o 'priority:[^|]*' | cut -d: -f2))\n\n"
        fi

        pr_briefs_content+="<details>\n"
        pr_briefs_content+="<summary>🤖 **點擊展開 PR 簡報 (可直接餵給 Coder Agent)**</summary>\n\n"
        pr_briefs_content+="$(generate_executable_pr_brief "$wp_id")\n\n"
        pr_briefs_content+="</details>\n\n"
    done

    pr_briefs_content+="---\n\n"
    pr_briefs_content+="### 📊 AI 分析摘要\n\n"
    pr_briefs_content+="**效率提升**: $EFFICIENCY_ANALYSIS\n\n"
    pr_briefs_content+="**並行執行順序**:\n"
    pr_briefs_content+="- **階段1 (立即並行)**: $PHASE_1_PACKAGES\n"
    pr_briefs_content+="- **階段2 (依序執行)**: $PHASE_2_PACKAGES\n"
    if [[ -n "$PHASE_3_PACKAGES" ]]; then
        pr_briefs_content+="- **階段3 (最終收尾)**: $PHASE_3_PACKAGES\n"
    fi
    pr_briefs_content+="\n"
    pr_briefs_content+="<!-- AI_PR_BRIEFS_END -->\n"

    # 將簡報內容發表為父任務評論
    echo "💬 在父任務 #$ISSUE_NUMBER 下發表 PR 簡報評論..."
    echo -e "$pr_briefs_content" | gh issue comment "$ISSUE_NUMBER" --body-file -

    echo "✅ PR 簡報已發表到父任務評論中"
}

function generate_executable_pr_brief() {
    local wp_id="$1"
    local details="${WORK_PACKAGE_DETAILS[$wp_id]}"

    # 解析工作包詳情 (v2.4 原子任務詳情整合版本)
    local title=$(echo "$details" | grep -o 'title:[^|]*' | cut -d: -f2)
    local priority=$(echo "$details" | grep -o 'priority:[^|]*' | cut -d: -f2)
    local time=$(echo "$details" | grep -o 'time:[^|]*' | cut -d: -f2)
    local tasks=$(echo "$details" | grep -o 'tasks:[^|]*' | cut -d: -f2)
    local tech_stack=$(echo "$details" | grep -o 'tech:[^|]*' | cut -d: -f2)
    local reason=$(echo "$details" | grep -o 'reason:[^|]*' | cut -d: -f2)
    local can_start=$(echo "$details" | grep -o 'can_start:[^|]*' | cut -d: -f2)
    
    # 🆕 v2.4: 解析原子任務詳情和執行上下文
    local atomic_tasks_json=$(echo "$details" | grep -o 'atomic_tasks_json:[^|]*' | cut -d: -f2-)
    local execution_context_json=$(echo "$details" | grep -o 'execution_context:[^|]*' | cut -d: -f2-)

    # 生成 PR 標題建議
    local pr_title_suggestion=""
    case "$priority" in
        "P0") pr_title_suggestion="fix" ;;
        "P1") pr_title_suggestion="feat" ;;
        "P2") pr_title_suggestion="docs" ;;
        *) pr_title_suggestion="chore" ;;
    esac

    cat <<EOF
#### **PR 標題 (建議)**
\`$pr_title_suggestion($tech_stack): [P#$ISSUE_NUMBER] $title\`

#### **PR 描述 (模板)**
\`\`\`markdown
Resolves work package "$wp_id" for parent issue #$ISSUE_NUMBER.

**工作包目標**: $title

**執行範圍**:
$(IFS=',' read -ra task_array <<< "$tasks"; for task_id in "${task_array[@]}"; do
    for task in "${ATOMIC_TASKS[@]}"; do
        if [[ "$task" =~ ^${task_id}: ]]; then
            local task_desc=$(echo "$task" | cut -d: -f2)
            echo "- $task_id: $task_desc"
            break
        fi
    done
done)

**AI 聚合理由**: $reason

**驗證完成**:
- [ ] 所有相關功能按要求實現
- [ ] 本地測試通過
- [ ] 代碼覆蓋率符合要求
- [ ] CI 檢查全部通過
\`\`\`

#### **工作指令 (Prompt for Coder Agent)**
\`\`\`
你是一位資深的 $tech_stack 開發專家。你的任務是完成以下工作包。

**核心目標**: $title

**技術棧**: $tech_stack
**優先級**: $priority
**預估時間**: $time

**🆕 v2.4 詳細任務清單**:
$(if [[ -n "$atomic_tasks_json" && "$atomic_tasks_json" != "null" ]]; then
    echo "$atomic_tasks_json" | jq -r '.[] | "
### " + .id + ": " + .title + "
**描述**: " + .description + "
**執行步驟**:
" + (.action_steps[] | "  - " + .) + "
**預期檔案**: " + (.expected_files | join(", ")) + "
**驗收標準**:
" + (.acceptance_criteria[] | "  - " + .) + "
**預估工作量**: " + .estimated_effort + "
"'
else
    # 向下相容：使用舊格式
    IFS=',' read -ra task_array <<< "$tasks"; for task_id in "${task_array[@]}"; do
        for task in "${ATOMIC_TASKS[@]}"; do
            if [[ "$task" =~ ^${task_id}: ]]; then
                local task_desc=$(echo "$task" | cut -d: -f2)
                echo "$((++counter)). $task_desc"
                break
            fi
        done
        counter=$((counter+1))
    done
fi)

**開始條件**:
$(if [[ "$can_start" == "true" ]]; then
    echo "✅ 可立即開始，無前置依賴"
else
    echo "⏳ 需要等待前置工作包完成："
    # 根據 prerequisite_packages 添加具體的等待條件
    IFS=',' read -ra pre_pkgs <<< "$prerequisite_packages"
    for pkg in "${pre_pkgs[@]}"; do
        echo "   - 等待 $pkg 完成"
    done
fi)

**🆕 v2.4 執行上下文**:
$(if [[ -n "$execution_context_json" && "$execution_context_json" != "null" && "$execution_context_json" != "{}" ]]; then
    echo "**相關檔案**: $(echo "$execution_context_json" | jq -r '.related_files[]?' | tr '\n' ', ' | sed 's/,$//')"
    echo "**環境設定**: $(echo "$execution_context_json" | jq -r '.environment_setup // "無特殊要求"')"
    echo "**常見陷阱**: $(echo "$execution_context_json" | jq -r '.common_pitfalls[]?' | sed 's/^/  - /')"
    echo "**測試要求**: $(echo "$execution_context_json" | jq -r '.testing_requirements // "遵循專案標準"')"
else
    echo "**上下文檔案 (AI 建議須加載)**:"
    get_contextual_files_for_work_package "$tasks"
fi)

**驗收標準 (DoD)**:
$(generate_work_package_dod "$wp_id")

**工程實踐要求**:
- 遵循 TDD 開發模式
- 確保代碼風格一致
- 所有測試必須通過
- 適當的錯誤處理
- 清晰的代碼註解
\`\`\`

#### **預期產出**
- 一個可合併的 PR，包含完整的功能實現
- 對應的測試覆蓋
- 相關文檔更新（如需要）
- CI 狀態為 ✅ 所有檢查通過

#### **估算工作量**: $time
EOF
}

### 2. 輔助函數：上下文檔案和驗收標準
```bash
function get_contextual_files_for_work_package() {
    local tasks="$1"

    echo "**AI 智能識別的相關檔案**:"
    IFS=',' read -ra task_array <<< "$tasks"
    local files_found=false

    for task_id in "${task_array[@]}"; do
        if [[ -n "${CONTEXTUAL_FILES[$task_id]}" ]]; then
            local files_info="${CONTEXTUAL_FILES[$task_id]}"
            local interface_files=$(echo "$files_info" | cut -d'|' -f1 | sed 's/interface://')
            local impl_files=$(echo "$files_info" | cut -d'|' -f2 | sed 's/impl://')
            local test_files=$(echo "$files_info" | cut -d'|' -f3 | sed 's/test://')

            if [[ -n "$interface_files" ]]; then
                echo "- **接口檔案**: $interface_files"
                files_found=true
            fi
            if [[ -n "$impl_files" ]]; then
                echo "- **參考實現**: $impl_files"
                files_found=true
            fi
            if [[ -n "$test_files" ]]; then
                echo "- **測試檔案**: $test_files"
                files_found=true
            fi
        fi
    done

    if [[ "$files_found" == "false" ]]; then
        echo "- 請根據工作包需求自行識別相關檔案"
    fi
}

function generate_work_package_dod() {
    local wp_id="$1"
    local details="${WORK_PACKAGE_DETAILS[$wp_id]}"
    local tasks=$(echo "$details" | grep -o 'tasks:[^|]*' | cut -d: -f2)
    local tech_stack=$(echo "$details" | grep -o 'tech:[^|]*' | cut -d: -f2)

    cat <<EOF
**AI 基於工作包特性生成的驗收標準**:
- [ ] ✅ **核心功能實現**: 所有相關任務的功能按要求實現
- [ ] 🧪 **TDD 測試完成**: 採用測試先行開發模式
- [ ] 📊 **測試覆蓋率**: 新代碼覆蓋率 ≥ 80%
- [ ] 🔄 **CI 檢查通過**: 所有自動化測試在 CI 環境執行成功
- [ ] 📝 **代碼風格**: 遵循項目代碼風格規範
- [ ] 🔗 **集成測試**: 與現有系統集成無問題

**技術棧特定標準 ($tech_stack)**:
$(case "$tech_stack" in
    "前端依賴管理"|"前端")
        echo "- [ ] 📦 依賴版本衝突解決"
        echo "- [ ] 🔒 安全漏洞修復驗證"
        echo "- [ ] 🧪 前端單元測試通過"
        ;;
    "CI/CD基礎設施"|"CI/CD")
        echo "- [ ] ⚙️ CI 配置語法正確"
        echo "- [ ] 🔄 CI 流程測試通過"
        echo "- [ ] 📊 性能影響最小化"
        ;;
    "外部集成與文檔"|"文檔")
        echo "- [ ] 📚 文檔內容準確完整"
        echo "- [ ] 🔗 外部服務狀態同步"
        echo "- [ ] ✅ 文檔格式規範"
        ;;
    *)
        echo "- [ ] 🎯 功能需求完全滿足"
        echo "- [ ] 🔧 代碼可維護性良好"
        echo "- [ ] 📋 相關文檔同步更新"
        ;;
esac)

**原子任務覆蓋檢查**:
$(IFS=',' read -ra task_array <<< "$tasks"; for task_id in "${task_array[@]}"; do
    for task in "${ATOMIC_TASKS[@]}"; do
        if [[ "$task" =~ ^${task_id}: ]]; then
            local task_desc=$(echo "$task" | cut -d: -f2)
            echo "- [ ] $task_id: $task_desc"
            break
        fi
    done
done)
EOF
}

### 3. 代碼清理完成 ✅
> **v2.4 重構**: 移除所有廢棄函數和孤立代碼片段，保持代碼庫整潔
>
> **清理項目**:
> - ❌ `generate_concise_html_report()` - 未被調用的 HTML 生成函數
> - ❌ `calculate_number_of_parallel_tracks()` - 未被調用的計算函數
> - ❌ `calculate_efficiency_improvement()` - 未被調用的計算函數
> - ❌ `generate_phase_prs()` 相關代碼片段 - 孤立的 PR 生成邏輯
> - ❌ `generate_AI_enhanced_pr_brief()` 相關函數 - 未完整實現的功能
> - ❌ `get_contextual_files_for_tasks()` 相關函數 - 殘留的工具函數
> - ❌ `generate_AI_completion_criteria()` 相關函數 - 未被調用的標準生成
>
> **保留功能**: 統一使用 `generate_comprehensive_html_report()` 生成完整分析報告
>
> **代碼結構**: 現在保持乾淨的單一執行路徑，專注於核心 Task-Dispatcher v2.4 功能
```

### 2. AI 增強的 HTML 報告生成
```bash
function generate_AI_enhanced_html_report() {
    echo "📊 生成 AI 增強的可視化報告..."

    cat > "$HTML_REPORT" <<EOF
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task-Dispatcher AI 推理優化報告 - Issue #$ISSUE_NUMBER</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body { font-family: 'Inter', sans-serif; max-width: 1400px; margin: 0 auto; padding: 20px; background: #f8fafc; }
        .header { text-align: center; background: linear-gradient(135deg, #6366f1, #8b5cf6, #ec4899); color: white; padding: 40px; border-radius: 16px; margin-bottom: 30px; box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3); }
        .ai-badge { background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: 600; margin-top: 10px; display: inline-block; }
        .section { background: white; margin: 20px 0; padding: 24px; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.08); border: 1px solid #e5e7eb; }
        .ai-insight { background: linear-gradient(135deg, #f0f9ff, #e0f2fe); border-left: 4px solid #0ea5e9; padding: 20px; border-radius: 8px; margin: 16px 0; }
        .optimization-card { background: #f0fdf4; border: 1px solid #bbf7d0; padding: 16px; border-radius: 8px; margin: 12px 0; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; }
        .metric-card { background: #fafafa; padding: 20px; border-radius: 8px; text-align: center; border: 1px solid #e5e7eb; }
        .metric-number { font-size: 32px; font-weight: bold; color: #6366f1; margin-bottom: 8px; }
        .metric-label { color: #6b7280; font-size: 14px; }
        .ai-reasoning { background: #f8fafc; border: 1px solid #d1d5db; padding: 16px; border-radius: 6px; margin: 8px 0; font-style: italic; }
        .mermaid { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #e5e7eb; }
        .phase-group { background: #f0f9ff; padding: 16px; border-radius: 8px; margin: 12px 0; border-left: 4px solid #3b82f6; }
        .decoupling-strategy { background: #fefce8; border: 1px solid #fde047; padding: 14px; border-radius: 6px; margin: 8px 0; }
        .task-item { background: white; border: 1px solid #e5e7eb; padding: 12px; border-radius: 6px; margin: 8px 0; }
        .ai-tag { background: #6366f1; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px; font-weight: 500; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 Task-Dispatcher AI 推理優化報告</h1>
        <div class="ai-badge">🤖 四重子代理 AI 協作</div>
        <p><strong>父任務</strong>: Issue #$ISSUE_NUMBER - $ISSUE_TITLE</p>
        <p><strong>生成時間</strong>: $(date +'%Y-%m-%d %H:%M:%S')</p>
        <p><strong>AI 模式</strong>: 動態推理 + 創造性優化</p>
    </div>

    <div class="section">
        <h2>🧠 AI 推理成果總覽</h2>
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-number">${#ATOMIC_TASKS[@]}</div>
                <div class="metric-label">AI 推理的原子任務</div>
            </div>
            <div class="metric-card">
                <div class="metric-number">$BOTTLENECK_LENGTH</div>
                <div class="metric-label">AI 識別的關鍵路徑</div>
            </div>
            <div class="metric-card">
                <div class="metric-number">${#PR_TASKS[@]}</div>
                <div class="metric-label">AI 優化的並行 PR</div>
            </div>
            <div class="metric-card">
                <div class="metric-number">$(echo "$PARALLEL_EFFICIENCY" | grep -o '[0-9]*%')</div>
                <div class="metric-label">預計效率提升</div>
            </div>
        </div>

        <div class="ai-insight">
            <h4>🧠 AI 核心洞察</h4>
            <p><strong>推理模式轉變</strong>: 從規則匹配升級為動態推理，能處理任何類型的技術任務</p>
            <p><strong>創造性優化</strong>: AI 主動提出解耦策略，而非被動計算</p>
            <p><strong>智能上下文</strong>: 自動識別相關代碼檔案，提升 Coder Agent 效率</p>
        </div>
    </div>

    <div class="section">
        <h2>⚡ AI 創造性解耦策略</h2>
        <div class="ai-insight">
            <h4>🎯 關鍵路徑分析</h4>
            <p><strong>AI 識別的瓶頸</strong>: $CRITICAL_PATH</p>
            <p><strong>瓶頸影響</strong>: $(echo "$OPTIMIZATION_JSON" | jq -r '.critical_path_analysis.impact_assessment')</p>
        </div>

        $(generate_decoupling_strategies_html)
    </div>

    <div class="section">
        <h2>🗺️ AI 優化的並行執行流程</h2>
        <div class="mermaid">
            $(generate_ai_optimized_mermaid)
        </div>
    </div>

    <div class="section">
        <h2>🚀 AI 推理的執行階段</h2>
        $(generate_ai_phases_html)
    </div>

    <div class="section">
        <h2>📋 AI 生成的執行任務</h2>
        $(generate_ai_tasks_html)
    </div>

    <div class="section">
        <h2>🧠 AI 子代理協作記錄</h2>
        <div class="ai-insight">
            <h4>四重子代理工作流程</h4>
            <div class="task-item">
                <span class="ai-tag">Task Analysis Agent</span>
                <strong>動態任務推理</strong>: 從 DoD 推理出 ${#ATOMIC_TASKS[@]} 個原子任務，100% 覆蓋驗收標準
            </div>
            <div class="task-item">
                <span class="ai-tag">Dependency Reasoning Agent</span>
                <strong>深度依賴分析</strong>: 基於技術邏輯推理依賴關係，區分硬/軟依賴
            </div>
            <div class="task-item">
                <span class="ai-tag">Optimization Strategy Agent</span>
                <strong>創造性優化</strong>: 提出 $(echo "$OPTIMIZATION_JSON" | jq '.decoupling_strategies | length') 個解耦策略，縮短關鍵路徑
            </div>
            <div class="task-item">
                <span class="ai-tag">Context Intelligence Agent</span>
                <strong>智能上下文</strong>: 為每個任務識別相關代碼檔案，提升開發效率
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'base',
            themeVariables: {
                primaryColor: '#6366f1',
                primaryTextColor: '#1f2937',
                primaryBorderColor: '#4f46e5',
                lineColor: '#6b7280',
                sectionBkgColor: '#f8fafc',
                altSectionBkgColor: '#f1f5f9'
            }
        });
    </script>
</body>
</html>
EOF

    echo "✅ AI 增強 HTML 報告已生成: $HTML_REPORT"
}

function generate_decoupling_strategies_html() {
    local strategy_count=$(echo "$OPTIMIZATION_JSON" | jq '.decoupling_strategies | length')

    for ((i=0; i<strategy_count; i++)); do
        local target=$(echo "$OPTIMIZATION_JSON" | jq -r ".decoupling_strategies[$i].target_dependency")
        local strategy=$(echo "$OPTIMIZATION_JSON" | jq -r ".decoupling_strategies[$i].strategy")
        local implementation=$(echo "$OPTIMIZATION_JSON" | jq -r ".decoupling_strategies[$i].implementation")
        local benefit=$(echo "$OPTIMIZATION_JSON" | jq -r ".decoupling_strategies[$i].benefit")
        local effort=$(echo "$OPTIMIZATION_JSON" | jq -r ".decoupling_strategies[$i].effort")

        cat <<EOF
<div class="decoupling-strategy">
    <h4>💡 策略 $((i+1)): $strategy</h4>
    <p><strong>目標依賴</strong>: $target</p>
    <p><strong>實施方案</strong>: $implementation</p>
    <p><strong>預期效益</strong>: $benefit</p>
    <p><strong>實施成本</strong>: $effort</p>
</div>
EOF
    done
}

function generate_ai_optimized_mermaid() {
    echo "graph TD"
    echo "    classDef phase1 fill:#dcfce7,stroke:#16a34a"
    echo "    classDef phase2 fill:#fef3c7,stroke:#d97706"
    echo "    classDef phase3 fill:#fecaca,stroke:#dc2626"

    # Phase 1 並行任務
    if [[ -n "$PHASE_1_TASKS" ]]; then
        echo "    subgraph \"Phase 1: 並行啟動 (AI 優化)\""
        IFS=',' read -ra task_array <<< "$PHASE_1_TASKS"
        for task_id in "${task_array[@]}"; do
            for task in "${ATOMIC_TASKS[@]}"; do
                if [[ "$task" =~ ^${task_id}: ]]; then
                    local task_desc=$(echo "$task" | cut -d: -f2 | cut -c1-20)
                    echo "        $task_id[\"$task_desc\"]:::phase1"
                    break
                fi
            done
        done
        echo "    end"
    fi

    # Phase 2 並行任務
    if [[ -n "$PHASE_2_TASKS" ]]; then
        echo "    subgraph \"Phase 2: 核心實現 (AI 優化)\""
        IFS=',' read -ra task_array <<< "$PHASE_2_TASKS"
        for task_id in "${task_array[@]}"; do
            for task in "${ATOMIC_TASKS[@]}"; do
                if [[ "$task" =~ ^${task_id}: ]]; then
                    local task_desc=$(echo "$task" | cut -d: -f2 | cut -c1-20)
                    echo "        $task_id[\"$task_desc\"]:::phase2"
                    break
                fi
            done
        done
        echo "    end"
    fi

    # Phase 3 收尾任務
    if [[ -n "$PHASE_3_TASKS" ]]; then
        echo "    subgraph \"Phase 3: 集成收尾 (AI 優化)\""
        IFS=',' read -ra task_array <<< "$PHASE_3_TASKS"
        for task_id in "${task_array[@]}"; do
            for task in "${ATOMIC_TASKS[@]}"; do
                if [[ "$task" =~ ^${task_id}: ]]; then
                    local task_desc=$(echo "$task" | cut -d: -f2 | cut -c1-20)
                    echo "        $task_id[\"$task_desc\"]:::phase3"
                    break
                fi
            done
        done
        echo "    end"
    fi

    # 生成依賴關係（只顯示硬依賴）
    for task_id in "${!HARD_DEPENDENCIES[@]}"; do
        local hard_deps="${HARD_DEPENDENCIES[$task_id]}"
        if [[ -n "$hard_deps" ]]; then
            IFS=',' read -ra dep_array <<< "$hard_deps"
            for dep in "${dep_array[@]}"; do
                echo "    $dep --> $task_id"
            done
        fi
    done
}

function generate_ai_phases_html() {
    cat <<EOF
<div class="phase-group">
    <h3>🚀 Phase 1: 並行啟動 (${PHASE_1_TASKS//,/ 個，} 個任務)</h3>
    <p><strong>AI 策略</strong>: 無依賴任務立即並行啟動，建立開發基礎</p>
    <div class="ai-reasoning">
        AI 推理: 這些任務可以完全並行執行，為後續階段奠定基礎
    </div>
</div>

<div class="phase-group">
    <h3>⚡ Phase 2: 核心實現 (${PHASE_2_TASKS//,/ 個，} 個任務)</h3>
    <p><strong>AI 策略</strong>: 基於解耦策略，核心功能並行開發</p>
    <div class="ai-reasoning">
        AI 推理: 通過接口定義和 Mock 策略，實現前後端並行開發
    </div>
</div>

<div class="phase-group">
    <h3>🎯 Phase 3: 集成收尾 (${PHASE_3_TASKS//,/ 個，} 個任務)</h3>
    <p><strong>AI 策略</strong>: 最終集成和驗證，確保整體功能完整</p>
    <div class="ai-reasoning">
        AI 推理: 集成任務需要前期實現完成，但可以增量集成降低風險
    </div>
</div>
EOF
}

function generate_ai_tasks_html() {
    echo "<div class=\"task-list\">"
    for issue_num in "${PR_TASKS[@]}"; do
        echo "<div class=\"task-item\">"
        echo "<span class=\"ai-tag\">AI 優化</span>"
        echo "<a href=\"https://github.com/MumuTW/novel-web/issues/$issue_num\">Issue #$issue_num</a>"
        echo "<span style=\"margin-left: 8px; color: #6b7280;\">- AI 推理並行任務</span>"
        echo "</div>"
    done
    echo "</div>"
}
```

---

## 🔧 主流程執行
```bash
# 🚀 主流程：Task-Dispatcher v2.3 適應性任務分派的標準作業程序 (SOP)
function main() {
    echo "🏗️ Task-Dispatcher v2.4 (The Analyst) - 任務分析與規劃專家啟動"
    echo "🎯 分析模式：$ANALYSIS_MODE"
    echo "📋 標準作業程序 (SOP v2.4)："
    echo "   1️⃣ 接收任務 (Receive Task)"
    echo "   2️⃣ 五重子代理分析 (Five-Agent Analysis)"
    echo "   3️⃣ 生成分析報告 (Generate Analysis Report)"
    echo "   4️⃣ 發布規劃通知 (Publish Planning Notice)"
    echo ""

    # 1️⃣ 接收任務 (已在 Stage 0 完成)
    echo "1️⃣ ✅ 任務接收：Issue #$ISSUE_NUMBER 狀態已驗證"

    # 2️⃣ 五重子代理分析
    echo "2️⃣ 🧠 五重子代理分析啟動..."
    echo "   🧠 Task Analysis Agent：任務分解分析..."
    dynamic_atomic_task_analysis

    echo "   🔗 Dependency Reasoning Agent：依賴關係推理..."
    AI_dependency_reasoning

    echo "   📦 Work Package Grouping Agent：工作包聚合..."
    AI_work_package_grouping

    echo "   ⚡ Optimization Strategy Agent：優化策略分析..."
    AI_optimization_strategy

    echo "   📚 Context Intelligence Agent：上下文識別..."
    AI_context_intelligence

    # 3️⃣ 生成分析報告
    echo "3️⃣ 📊 生成分析報告..."
    if [[ "$ANALYSIS_MODE" == "codex" ]]; then
        echo "📋 生成結構化工作包評論（Codex 模式）..."
        generate_work_package_pr_briefs
    else
        echo "📊 生成詳盡的 HTML 分析報告（預設模式）..."
        generate_comprehensive_html_report
    fi

    # 4️⃣ 發布規劃通知
    echo "4️⃣ 📢 發布規劃通知..."
    update_parent_task_status

    echo ""
    echo "🎉 Task-Dispatcher 分析完成！"
    if [[ "$ANALYSIS_MODE" == "codex" ]]; then
        echo "🏗️ 輸出模式: 結構化工作包評論"
        echo "📦 工作包數量: ${#WORK_PACKAGES[@]} 個"
        echo "📋 下一步: Coder Agent 可直接使用工作包評論"
    else
        echo "📊 輸出模式: 詳盡 HTML 分析報告"
        echo "📄 報告位置: docs/04_AI_OPERATIONS/task-dispatcher/"
        echo "💡 下一步: 人類指揮官和 Coder Agent 可查閱完整分析"
    fi
}

function generate_comprehensive_html_report() {
    echo "📊 生成詳盡的 HTML 分析報告..."

    # 設置報告路徑
    local report_date=$(date +'%Y-%m-%d')
    local report_dir="docs/04_AI_OPERATIONS/task-dispatcher"
    local report_file="$report_dir/Issue-${ISSUE_NUMBER}-dispatch-report.html"
    mkdir -p "$report_dir"

    # 生成詳盡的 HTML 分析報告
    cat > "$report_file" <<EOF
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Issue #${ISSUE_NUMBER} - 綜合分析報告</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif; margin: 40px; line-height: 1.6; background: #f8f9fa; }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 40px; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }
        h1 { color: #0366d6; border-bottom: 3px solid #0366d6; padding-bottom: 15px; font-size: 2.2em; }
        h2 { color: #24292e; border-left: 4px solid #0366d6; padding-left: 15px; margin-top: 40px; font-size: 1.5em; }
        h3 { color: #586069; font-size: 1.2em; }
        .executive-summary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 12px; margin-bottom: 30px; }
        .work-package { background: #f6f8fa; padding: 20px; margin: 15px 0; border-left: 4px solid #28a745; border-radius: 8px; }
        .dependency { background: #fff3cd; padding: 15px; margin: 10px 0; border-left: 4px solid #ffc107; border-radius: 6px; }
        .optimization { background: #d4edda; padding: 20px; margin: 15px 0; border-left: 4px solid #28a745; border-radius: 8px; }
        .context-file { background: #e7f3ff; padding: 12px; margin: 8px 0; border-left: 4px solid #0366d6; border-radius: 6px; }
        .metadata { background: #f8f9fa; padding: 25px; border-radius: 10px; margin: 25px 0; border: 2px solid #e1e4e8; }
        .phase-chart { background: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0; }
        code { background: #f6f8fa; padding: 3px 8px; border-radius: 4px; font-family: 'Monaco', 'Menlo', monospace; }
        .efficiency-badge { background: #28a745; color: white; padding: 5px 10px; border-radius: 15px; font-size: 0.9em; }
        .task-item { background: #ffffff; padding: 15px; margin: 8px 0; border: 1px solid #e1e4e8; border-radius: 6px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="executive-summary">
            <h1 style="color: white; border: none; margin: 0; font-size: 2em;">📊 Task-Dispatcher v2.4 綜合分析報告</h1>
            <p style="margin: 15px 0 5px 0; font-size: 1.1em;"><strong>任務：</strong>Issue #${ISSUE_NUMBER}</p>
            <p style="margin: 5px 0;"><strong>分析師：</strong>Task-Dispatcher v2.4 "The Analyst"</p>
            <p style="margin: 5px 0;"><strong>目標讀者：</strong>人類指揮官 + Coder Agent</p>
        </div>

        <h1>Issue #${ISSUE_NUMBER} 綜合分析報告</h1>

        <div class="metadata">
            <h3>📋 執行摘要</h3>
            <p><strong>Issue 標題：</strong>$ISSUE_TITLE</p>
            <p><strong>分析時間：</strong>$(date +'%Y-%m-%d %H:%M:%S')</p>
            <p><strong>效率提升：</strong><span class="efficiency-badge">預計提升 \${EFFICIENCY_ANALYSIS:-25-40%}</span></p>
            <p><strong>工作包數量：</strong>\${#WORK_PACKAGES[@]} 個邏輯工作包</p>
            <p><strong>並行機會：</strong>已識別最佳並行執行策略</p>
        </div>

        <h2>📦 工作包列表</h2>
        <p>原子任務已按邏輯聚合為以下工作包，方便理解和分派：</p>
$(for wp_id in "${WORK_PACKAGES[@]}"; do
    local details="${WORK_PACKAGE_DETAILS[$wp_id]}"
    local title=$(echo "$details" | cut -d'|' -f1 | sed 's/title://')
    local priority=$(echo "$details" | cut -d'|' -f2 | sed 's/priority://')
    local time=$(echo "$details" | cut -d'|' -f3 | sed 's/time://')
    local tasks=$(echo "$details" | cut -d'|' -f4 | sed 's/tasks://')
    local can_start=$(echo "$details" | cut -d'|' -f5 | sed 's/can_start://')
    echo "        <div class=\"work-package\">"
    echo "            <h4>$wp_id: $title</h4>"
    echo "            <p><strong>優先級：</strong>$priority | <strong>預估時間：</strong>$time</p>"
    echo "            <p><strong>可立即開始：</strong>$can_start</p>"
    echo "            <p><strong>包含任務：</strong>$tasks</p>"
    echo "        </div>"
done)

        <h2>🔄 並行階段圖</h2>
        <div class="phase-chart">
            <h4>推薦執行順序和並行機會</h4>
            <div class="mermaid">
                graph LR
                    subgraph "階段 1: 並行啟動"
                        $(echo "$PHASE_1_PACKAGES" | tr ',' '\n' | while read pkg; do echo "A[$pkg]"; done | tr '\n' ' ')
                    end
                    subgraph "階段 2: 依序執行"
                        $(echo "$PHASE_2_PACKAGES" | tr ',' '\n' | while read pkg; do echo "B[$pkg]"; done | tr '\n' ' ')
                    end
                    subgraph "階段 3: 收尾整合"
                        $(echo "$PHASE_3_PACKAGES" | tr ',' '\n' | while read pkg; do echo "C[$pkg]"; done | tr '\n' ' ')
                    end
            </div>
            <p><strong>並行策略說明：</strong>$EFFICIENCY_ANALYSIS</p>
        </div>

        <h2>🔗 依賴關係詳情</h2>
        <p>每個工作包的前置依賴和阻塞關係：</p>
$(for task_id in "${!HARD_DEPENDENCIES[@]}"; do
    echo "        <div class=\"dependency\">"
    echo "            <h4>$task_id 的依賴關係</h4>"
    echo "            <p><strong>硬依賴：</strong>${HARD_DEPENDENCIES[$task_id]}</p>"
    echo "            <p><strong>軟依賴：</strong>${SOFT_DEPENDENCIES[$task_id]}</p>"
    echo "            <p><strong>依賴原因：</strong>${DEPENDENCY_REASONING[$task_id]}</p>"
    echo "        </div>"
done)

        <h2>⚡ 優化策略分析</h2>
        <div class="optimization">
            <h4>AI 推薦的解耦和並行優化策略</h4>
            <p><strong>關鍵路徑識別：</strong>已分析最長依賴鏈並提出解耦建議</p>
            <p><strong>並行機會：</strong>識別可同時進行的工作包</p>
            <p><strong>風險緩解：</strong>提供接口先行定義、Mock 數據等解耦策略</p>
        </div>

        <h2>📚 上下文檔案清單</h2>
        <p>執行此任務需要參考或修改的關鍵檔案：</p>
$(for file in "${CONTEXTUAL_FILES[@]}"; do
    echo "        <div class=\"context-file\">"
    echo "            <code>$file</code>"
    echo "        </div>"
done)

        <h2>📋 完整原子任務清單（附錄）</h2>
        <details style="margin-top: 30px;">
            <summary><strong>展開查看所有原子任務詳情</strong></summary>
$(for task in "${ATOMIC_TASKS[@]}"; do
    local task_id=$(echo "$task" | cut -d: -f1)
    local task_desc=$(echo "$task" | cut -d: -f2)
    local task_type=$(echo "$task" | cut -d: -f3)
    local task_effort=$(echo "$task" | cut -d: -f4)
    echo "            <div class=\"task-item\">"
    echo "                <h4>$task_id: $task_desc</h4>"
    echo "                <p><strong>類型：</strong>$task_type | <strong>預估工作量：</strong>$task_effort</p>"
    echo "            </div>"
done)
        </details>

        <div class="metadata" style="margin-top: 40px;">
            <h3>🚀 執行指南</h3>
            <p><strong>人類指揮官：</strong>可使用此報告進行任務分派決策和進度監控</p>
            <p><strong>Coder Agent：</strong>可參考工作包列表和依賴關係執行具體開發工作</p>
            <p><strong>建議執行順序：</strong>按照並行階段圖的推薦順序執行</p>
            <p><strong>風險控制：</strong>遵循依賴關係，確保前置條件滿足後再開始下一階段</p>
        </div>
    </div>
</body>
</html>
EOF

    # 初始化 Mermaid 圖表
    cat >> "$report_file" <<'MERMAID_INIT'
    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#0366d6',
                primaryTextColor: '#24292e',
                primaryBorderColor: '#d1d5da',
                lineColor: '#6a737d'
            }
        });
    </script>
MERMAID_INIT

    echo "✅ 詳盡的 HTML 分析報告已生成："
    echo "📄 $report_file"

    # 設置全局變數供後續使用
    COMPREHENSIVE_REPORT_FILE="$report_file"
}

function update_parent_task_status() {
    echo "📢 發布規劃完成通知..."

    # 根據模式添加不同的標籤
    if [[ "$ANALYSIS_MODE" == "codex" ]]; then
        gh issue edit "$ISSUE_NUMBER" --add-label "ai:analysis-completed" --add-label "mode:codex"

        # 發布工作包評論（Codex 模式）
        local comment_body="## 🤖 Task-Dispatcher v2.4 分析完成

**父任務**: #${ISSUE_NUMBER}
**分析模式**: Codex（結構化工作包）
**工作包數量**: ${#WORK_PACKAGES[@]} 個

✅ 任務分析已完成，工作包評論已發布於上方
📋 Coder Agent 可直接使用工作包評論進行開發"

        gh issue comment "$ISSUE_NUMBER" --body "$comment_body"
    else
        gh issue edit "$ISSUE_NUMBER" --add-label "ai:analysis-completed" --add-label "mode:report"

        # 獲取報告相對路徑
        local report_relative_path=$(echo "$COMPREHENSIVE_REPORT_FILE" | sed "s|^.*/NovelWebsite/||")

        # 發布分析報告通知（預設模式）
        local comment_body="## 📊 Task-Dispatcher v2.4 綜合分析完成

**父任務**: #${ISSUE_NUMBER}
**分析師**: Task-Dispatcher v2.4 \"The Analyst\"
**目標讀者**: 人類指揮官 + Coder Agent

### 📋 分析成果
- ✅ **五重子代理分析**：任務分解、依賴推理、工作包聚合、優化策略、上下文識別
- 📊 **詳盡分析報告**：包含執行摘要、工作包列表、並行階段圖、依賴關係詳情
- ⚡ **效率提升預估**：${EFFICIENCY_ANALYSIS:-25-40%}
- 📦 **工作包數量**：${#WORK_PACKAGES[@]} 個邏輯工作包

### 📄 分析報告
🔗 **[查看完整分析報告]($report_relative_path)**

報告包含：
- 📋 執行摘要與關鍵指標
- 📦 邏輯工作包列表
- 🔄 並行階段圖（可視化）
- 🔗 依賴關係詳情
- 📚 上下文檔案清單
- 📋 完整原子任務清單（附錄）

### 🚀 下一步
- **人類指揮官**：可基於分析報告進行任務分派決策
- **Coder Agent**：可參考工作包列表和依賴關係執行開發工作
- **建議執行順序**：按照報告中的並行階段圖執行

---
🤖 Generated by Task-Dispatcher v2.4 \"The Analyst\""

        gh issue comment "$ISSUE_NUMBER" --body "$comment_body"
    fi

    echo "✅ 父任務已標記為分析完成，通知已發布"
}

function update_parent_issue_with_AI_insights() {
    echo "📊 更新父任務 Issue，報告任務分解結果..."

    local sub_tasks_list=""
    for issue_num in "${PR_TASKS[@]}"; do
        local issue_title=$(gh issue view "$issue_num" --json title -q ".title")
        sub_tasks_list+="- [ ] **#${issue_num}**: ${issue_title}\n"
    done

    local comment_body=$(cat <<EOF
<!-- AI_TASK_PLAN_START -->
## 🤖 Task-Dispatcher AI 推理優化完成

**父任務**: #${ISSUE_NUMBER}
**AI 分析報告**: [查看詳細 HTML 報告]($HTML_REPORT)
**執行策略**: 任務已基於 AI 並行優化策略，分解為以下 **原生 GitHub 子任務**。

---

### ✅ 已創建的子任務 (Sub-Issues)

${sub_tasks_list}

### 下一步
請開發團隊（或 `Coder` Agents）直接處理上述子任務。當所有子任務完成後，此父任務 (#${ISSUE_NUMBER}) 將被視為完成。

`Project Manager` 已將所有子任務同步至專案看板，並繼承了父任務的優先級。
<!-- AI_TASK_PLAN_END -->
EOF
)

    gh issue comment "$ISSUE_NUMBER" --body "$comment_body"
    echo "✅ 父任務已更新，包含子任務列表。"
}

# 模擬函數（實際實現中可以替換為真實的 LLM API 調用）
function generate_ai_dod_inference() {
    echo "基於 Issue 描述推理的 DoD..."
}

function validate_existing_dod() {
    echo "$DOD_SECTION"
}

# 🔧 全局變量初始化 (v2.2 工作包導向)
declare -a ATOMIC_TASKS
declare -a WORK_PACKAGES              # 🆕 工作包列表
declare -A WORK_PACKAGE_DETAILS       # 🆕 工作包詳情
declare -A HARD_DEPENDENCIES
declare -A SOFT_DEPENDENCIES
declare -A DEPENDENCY_REASONING
declare -A CONTEXTUAL_FILES
declare -A ATOMIC_TASKS_MAP           # 🆕 優化查找性能的哈希表

# JSON 數據存儲
ATOMIC_TASKS_JSON=""
DEPENDENCY_JSON=""
OPTIMIZATION_JSON=""
CONTEXT_JSON=""
WORK_PACKAGES_JSON=""                 # 🆕 工作包 JSON

# 並行執行計劃 (工作包級別)
PHASE_1_PACKAGES=""                   # 🆕 階段1工作包
PHASE_2_PACKAGES=""                   # 🆕 階段2工作包
PHASE_3_PACKAGES=""                   # 🆕 階段3工作包
EFFICIENCY_ANALYSIS=""                # 🆕 效率分析

# 舊版本變量 (保留兼容性)
CRITICAL_PATH=""
BOTTLENECK_LENGTH=""
PARALLEL_EFFICIENCY=""

# 執行主流程
main
```

---

## 🚀 **Task-Dispatcher v2.4 核心變革總結**

### 💡 **專業分析師：深度分析與智能規劃**

**v2.4 SOP 標準作業程序**：
1. **📥 接收任務**：接收一個父任務 Issue
2. **🧠 五重分析**：運行完整的「五重子代理」深度分析
3. **📊 生成報告**：產出詳盡的 HTML 分析報告（預設）
4. **📢 發布通知**：在父任務下發表規劃完成評論

**🆕 v2.4 核心價值**：
- ✅ **詳盡分析**：包含執行摘要、工作包列表、並行階段圖、依賴關係詳情
- ✅ **雙重受眾**：同時服務人類指揮官和 Coder Agent
- ✅ **可視化圖表**：Mermaid 並行階段圖，直觀展示執行策略
- ✅ **🆕 原子任務詳情整合**：工作包內包含完整的執行步驟、驗收標準和執行上下文
- ✅ **🆕 agent 友善設計**：讓領取工作包的 agent 明確知道具體要完成什麼，減少理解成本
- ✅ **智能聚合**：保留工作包概念，有助於任務組織和理解

### 🏗️ **五重子代理分析架構**
1. **🧠 Task Analysis Agent** - 從 DoD 推理原子任務清單
2. **🔗 Dependency Reasoning Agent** - 分析任務間依賴關係
3. **📦 Work Package Grouping Agent** - 智能聚合邏輯工作包
4. **⚡ Optimization Strategy Agent** - 提出並行優化策略
5. **📚 Context Intelligence Agent** - 識別相關檔案和上下文

### 📊 **🆕 v2.4 報告核心特性**
- **📋 執行摘要**：效率提升等關鍵指標
- **📦 🆕 增強工作包列表**：原子任務按邏輯聚合，內含完整執行詳情
- **🔧 🆕 詳細執行步驟**：每個原子任務包含具體操作步驟和驗收標準
- **🎯 🆕 執行上下文**：提供環境設定、常見陷阱、測試要求等關鍵資訊
- **🔄 並行階段圖**：可視化展示推薦的執行順序和並行機會
- **🔗 依賴關係詳情**：清晰列出每個工作包的前置依賴
- **📚 完整附錄**：原子任務清單和上下文文件供隨時查閱

### 🎯 **實際工作流程**
1. **Navigator** 創建父任務 (Epic)
2. **Task-Dispatcher** 運行五重分析，生成詳盡 HTML 報告
3. **人類指揮官** 基於報告進行任務分派決策
4. **Coder Agent** 參考工作包列表和依賴關係執行開發
5. **追蹤** 通過父任務和分析報告監控進度

### 🆕 **v2.4 模式選擇**
- **預設模式**：`/task-dispatcher <issue>` - 詳盡的 HTML 分析報告
- **Codex 模式**：`/task-dispatcher <issue> codex` - 結構化工作包評論（向下相容）

> **🆕 v2.4 新使命**：成為「任務分析與規劃專家」，將複雜任務轉化為清晰且可直接執行的藍圖

## 🎉 v2.4 版本總結

這個 v2.4 版本實現了**專業分析與實用規劃的完美平衡**，從「工程發包專家」進化為「任務分析與規劃專家」！

**🔥 v2.4 核心突破**：
- ✅ **原子任務詳情整合**：工作包內直接包含完整執行步驟
- ✅ **agent 友善設計**：減少理解成本，提高執行效率
- ✅ **執行上下文完整**：提供環境設定、常見陷阱、測試要求
- ✅ **向下相容**：保持與舊版本的相容性

**🚀 實際效益**：
- 領取工作包的 agent 能立即明確自己要做什麼
- 詳細的執行步驟減少實作過程中的猜測
- 完整的驗收標準確保任務完成品質
- 執行上下文避免常見陷阱，提高成功率

📊

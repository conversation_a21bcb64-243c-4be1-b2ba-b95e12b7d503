# Regex matches vendor and build directories so hooks skip third-party code
exclude: &exclude '(?x)^(?:.*/)?(?:node_modules|coverage|build|dist|sessionmanager-bundle|venv|storybook-static)/'

default_stages: [pre-commit]
repos:
  # -------------------------------------------------------------
  # 本地自訂 Hooks (Local Hooks)
  # -------------------------------------------------------------
  - repo: local
    hooks:
      - id: sensitive-data-check
        name: 🔍 敏感信息檢查 (僅掃描本次提交)
        entry: ./scripts/check_sensitive.sh
        language: script
        pass_filenames: true # 只將本次提交的檔名傳遞給腳本
        stages: [pre-commit]
        description: "檢查提交中是否包含API keys、tokens或其他敏感信息"
        exclude: '(?x)^(scripts/check_sensitive\.sh|scripts/maintenance/check_sensitive\.sh|.*/?node_modules/|.*/?coverage/|.*/?build/|.*/?dist/|.*/?sessionmanager-bundle/|.*/?venv/|.*/?storybook-static/)'

      - id: block-backend-imports
        name: 🚫 阻擋 backend.* 導入 (僅掃描本次提交)
        entry: bash -c 'py_files=$(for f in "$@"; do if [[ "$f" == *.py ]]; then echo "$f"; fi; done); if [ -n "$py_files" ]; then if grep -l "from backend\." $py_files; then echo "❌ 發現 backend.* 導入，請使用相對路徑"; exit 1; fi; fi'
        language: system
        pass_filenames: true # 只將本次提交的檔名傳遞給腳本
        types: [python] # 只對 Python 檔案觸發
        stages: [pre-commit]
        description: "防止提交包含 backend.* 導入的程式碼"

      - id: legacy-path-scanner
        name: 🔍 遺留路徑掃描 (部署安全網防線二)
        entry: ./scripts/ci/legacy-path-scanner.sh
        language: script
        pass_filenames: false # 掃描整個專案
        stages: [pre-commit]
        description: "檢查是否存在 novel.* 等遺留路徑引用，防止部署時出現模組找不到的問題"
        exclude: '(?x)^(scripts/ci/legacy-path-scanner\.sh|.*/?node_modules/|.*/?coverage/|.*/?build/|.*/?dist/|.*/?sessionmanager-bundle/|.*/?venv/|.*/?storybook-static/|.*\.md$)'

  # -------------------------------------------------------------
  # 通用程式碼品質與檔案檢查
  # -------------------------------------------------------------
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      # --- 文件格式 ---
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
        exclude: *exclude
      # --- Git 防護 ---
      - id: check-added-large-files
        name: 🛡️ 大檔案檢查 (>5MB)
        args: ['--maxkb=5000']
        exclude: *exclude
      - id: check-case-conflict
      - id: check-merge-conflict

  # -------------------------------------------------------------
  # Python 相關工具
  # -------------------------------------------------------------
  - repo: https://github.com/psf/black
    rev: 24.4.2
    hooks:
      - id: black
        language_version: python3
        exclude: *exclude

  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        exclude: *exclude
        args: ['--extend-ignore=E402,E203']

  # -------------------------------------------------------------
  # 前端相關工具
  # -------------------------------------------------------------
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.56.0 # 使用實際存在的最接近v8版本
    hooks:
      - id: eslint
        name: 💅 ESLint 檢查
        files: ^frontend/src/.*\.(js|jsx|ts|tsx)$
        exclude: *exclude
        args: [--config=frontend/eslint.config.js]
        # additional_dependencies 已徹底移除，package.json 為單一事實來源

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.1.0
    hooks:
      - id: prettier
        name: 💅 Prettier 格式化
        files: ^frontend/.*\.(js|jsx|ts|tsx|json|md|css)$ # 限制在前端目錄
        exclude: *exclude
        additional_dependencies: ["prettier@3.2.5"] # 鎖定版本

# NovelWebsite 專案結構

*最後更新: 2025-06-27 - 基於遠端 main 穩定版本同步 (commit: 2033946d7)*

## 📊 專案概覽

**專案狀態**: ✅ Backend/Novel 模組日落遷移完成，Monorepo 基礎架構穩定
**統計**: 188 個目錄, 647 個檔案
**架構**: 現代化 pnpm workspace + Turborepo monorepo + Django config 重構

## 🏗️ 核心目錄結構

```
.
├── .actrc                            # Act 配置檔案
├── .claude/                          # Claude AI 配置和命令
│   ├── commands/                     # Claude 自訂命令庫 (navigator-mvp, task-dispatcher等)
│   └── settings.json                 # Claude 設定檔案
├── .cursor/                          # Cursor 編輯器配置
├── .github/                          # GitHub Actions CI/CD 工作流程
│   ├── actions/run-in-docker/        # 可重用 Docker Action
│   └── workflows/                    # CI/CD 工作流程定義
├── apps/                             # 🎯 Monorepo 應用程式目錄
│   └── web/                          # ⚛️ React 前端應用 (從 frontend/ 遷移)
│       ├── .storybook/               # Storybook 配置
│       ├── src/                      # 前端原始碼
│       │   ├── components/           # React 組件
│       │   ├── pages/                # 頁面組件
│       │   └── services/             # API 服務
│       └── tests/e2e/                # E2E 測試
├── archive/                          # 封存舊程式碼
│   └── legacy-code-backup/           # 歷史程式碼備份
├── backend/                          # Django 後端應用
│   ├── apps/catalog/                 # 📚 核心業務邏輯 (小說目錄)
│   ├── config/                       # Django 配置管理 (統一設定入口)
│   │   ├── django_settings.py       # 主要 Django 設定檔
│   │   ├── wsgi.py                   # WSGI 應用入口
│   │   └── urls.py                   # 根 URL 配置
│   └── crawler_engine/               # 🕷️ Scrapy 爬蟲引擎
├── docs/                             # 📚 專案文檔 (層級架構 00-04)
│   ├── 00_SYSTEM_BLUEPRINT/          # 系統藍圖 (MOSES_TABLETS + SIBYLLINE_ORACLES)
│   ├── 01_PRODUCT_DESIGN/            # 產品設計 (PRD + MVP 清單)
│   ├── 02_ARCHITECTURE/              # 架構文檔 (專案結構 + 配置)
│   ├── 03_DEVELOPMENT_GUIDES/        # 開發指南 (時間線 + 爬蟲文檔)
│   ├── 04_AI_OPERATIONS/             # AI 操作 (Navigator + Task-Dispatcher)
│   └── _archive/                     # 封存文檔
├── frontend/                         # ⚛️ 舊版前端 (保留相容性)
├── packages/                         # 🔧 Monorepo 共享套件目錄 (未來擴展)
├── infra/                            # 🏗️ 基礎設施配置
│   ├── aws-ecr/                      # AWS ECR 配置
│   ├── docker/                       # Docker 配置和 Dockerfile
│   ├── monitoring/                   # 監控配置
│   └── templates/                    # 基礎設施模板
├── scripts/                          # 🛠️ 自動化腳本
│   ├── deployment/                   # 部署腳本
│   ├── maintenance/                  # 維護腳本
│   ├── setup/                        # 設置腳本
│   └── testing/                      # 測試腳本
├── tests/                            # 整合測試
├── uploads/                          # 上傳檔案存放區
├── CLAUDE.md                         # 🤖 AI 開發指南
├── Makefile                          # 🔨 統一開發工具入口
├── package.json                      # pnpm workspace 根配置
├── pnpm-workspace.yaml               # 🎯 Monorepo workspace 配置
├── turbo.json                        # ⚡ Turborepo 管道配置
├── README.md                         # 專案說明文檔
└── TECH_DEBT.md                      # 技術債務追蹤
```

## 🎯 重點目錄說明

### 🤖 AI 開發工具配置
- `.claude/commands/` - 包含各種 Claude 自訂命令 (navigator-mvp, task-dispatcher, doc-sync 等)
- `.cursor/` - Cursor 編輯器 MCP 配置和開發規則

### 📚 文檔層級架構 (00-04 系統)
- `docs/00_SYSTEM_BLUEPRINT/` - 系統藍圖 (摩西石板 + 預言神諭)
- `docs/01_PRODUCT_DESIGN/` - 產品設計文檔 (PRD + MVP 清單)
- `docs/02_ARCHITECTURE/` - 架構文檔 (專案結構 + 配置說明)
- `docs/03_DEVELOPMENT_GUIDES/` - 開發指南 (開發時間線 + 爬蟲文檔)
- `docs/04_AI_OPERATIONS/` - AI 操作文檔 (Navigator + Task-Dispatcher 報告)

### ⚙️ CI/CD 工作流程
- `.github/workflows/main-ci.yml` - Tier 2 極速 CI 流程 (8秒執行)
- `.github/actions/run-in-docker/` - 可重用 Docker Action，減少重複代碼

### 🐍 Django 後端 (已完成重構)
- `backend/apps/catalog/` - 核心小說目錄業務邏輯
- `backend/config/` - ✅ 統一 Django 設定管理 (取代 novel 模組)
  - `django_settings.py` - 主要設定檔
  - `wsgi.py` - WSGI 應用入口 (config.wsgi.application)
  - `urls.py` - 根 URL 配置
- `backend/crawler_engine/` - 重構後的 Scrapy 爬蟲系統

### ⚛️ Monorepo 應用結構
- `apps/web/` - 主要前端應用 (從 frontend/ 遷移)
- `apps/web/src/components/` - React 組件 (common, novel, reader 分類)
- `apps/web/.storybook/` - Storybook 元件展示系統
- `packages/` - 共享套件目錄 (未來擴展)

### 🏗️ 基礎設施
- `infra/docker/` - 多階段 Dockerfile 和建構腳本
- `infra/aws-ecr/` - AWS ECR 映像倉庫配置
- `infra/monitoring/` - Prometheus 監控配置

### 🛠️ 自動化腳本
- `scripts/deployment/` - 部署相關腳本
- `scripts/maintenance/` - 系統維護腳本
- `scripts/testing/` - 測試執行腳本

## 📊 專案特色

### 技術架構
- **Monorepo**: pnpm workspace + Turborepo 管理
- **後端**: Django 4.2+ + Scrapy 2.11+
- **前端**: React 18 + TypeScript (準備 Next.js 遷移)
- **CI/CD**: Tier 2 架構，智能緩存，極速執行
- **容器化**: Docker 多階段建構，ECR 映像管理

### 開發工具
- **AI 輔助**: Claude commands + Cursor MCP 整合
- **品質控制**: ESLint + Prettier + pre-commit hooks
- **測試策略**: Jest + Playwright + Storybook
- **監控**: Prometheus + Lighthouse + Percy

### 安全性
- **密鑰管理**: Doppler 整合
- **安全掃描**: 週期性漏洞檢查
- **存取控制**: AWS OIDC 無密鑰認證

---

這個架構代表了一個現代化、可擴展的全端小說網站專案，具備完整的 CI/CD 流程、AI 輔助開發環境和企業級的基礎設施管理能力。
